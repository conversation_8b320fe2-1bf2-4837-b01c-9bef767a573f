import React, { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { 
  OrbitControls, 
  PerspectiveCamera, 
  Environment, 
  ContactShadows, 
  Sparkles,
  Html,
  useTexture,
  MeshReflectorMaterial
} from '@react-three/drei';
import { motion, AnimatePresence } from 'framer-motion';
import { Group, Vector3 } from 'three';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Volume2, 
  VolumeX, 
  RotateCcw, 
  ZoomIn, 
  ZoomOut, 
  Camera, 
  Settings, 
  Palette,
  Play,
  Pause,
  Maximize,
  Eye,
  Sparkles as SparklesIcon
} from 'lucide-react';

interface CarCustomization {
  bodyColor?: string;
  hasRacingStripes?: boolean;
  hasAustralianFlag?: boolean;
  wheelSize?: number;
  exhaustType?: string;
  paintFinish?: 'matte' | 'metallic' | 'chrome' | 'carbon';
  underglow?: boolean;
  spoiler?: boolean;
}

interface Enhanced3DCarViewerProps {
  className?: string;
  isRevving?: boolean;
  onRevEngine?: () => void;
  customization?: CarCustomization;
  autoRotate?: boolean;
  showControls?: boolean;
  quality?: 'low' | 'medium' | 'high' | 'ultra';
  environment?: 'garage' | 'showroom' | 'track' | 'sunset' | 'city';
}

// Enhanced RX-3 model with better graphics
const EnhancedRX3Model = ({
  rotationSpeed = 0.002,
  isRevving = false,
  customization = {},
  autoRotate = true
}) => {
  const group = useRef<Group>(null);
  const [hovering, setHovering] = useState(false);
  const [enginePulse, setEnginePulse] = useState(0);

  // Animation frame
  useFrame((state) => {
    if (group.current) {
      // Auto rotation
      if (autoRotate) {
        group.current.rotation.y += rotationSpeed;
      }

      // Engine revving effects
      if (isRevving) {
        const pulse = Math.sin(state.clock.elapsedTime * 20) * 0.02;
        group.current.position.y = pulse;
        group.current.rotation.z = pulse * 0.1;
        setEnginePulse(Math.abs(pulse));
      } else {
        group.current.position.y = 0;
        group.current.rotation.z = 0;
        setEnginePulse(0);
      }

      // Hover effects
      if (hovering) {
        group.current.position.y += Math.sin(state.clock.elapsedTime * 2) * 0.01;
      }
    }
  });

  // Material properties based on customization
  const getPaintMaterial = () => {
    const baseColor = customization.bodyColor || '#FF0000';
    
    switch (customization.paintFinish) {
      case 'matte':
        return { color: baseColor, metalness: 0.1, roughness: 0.8 };
      case 'metallic':
        return { color: baseColor, metalness: 0.9, roughness: 0.1 };
      case 'chrome':
        return { color: '#C0C0C0', metalness: 1.0, roughness: 0.0 };
      case 'carbon':
        return { color: '#1a1a1a', metalness: 0.8, roughness: 0.2 };
      default:
        return { color: baseColor, metalness: 0.6, roughness: 0.2 };
    }
  };

  const paintMaterial = getPaintMaterial();
  const wheelScale = customization.wheelSize ? (customization.wheelSize / 17) : 1;
  const glowIntensity = isRevving ? 0.5 + enginePulse * 2 : (hovering ? 0.3 : 0);

  return (
    <group
      ref={group}
      onPointerOver={() => setHovering(true)}
      onPointerOut={() => setHovering(false)}
      position={[0, -1, 0]}
    >
      {/* Particle effects for revving */}
      {isRevving && (
        <Sparkles
          count={50}
          scale={[8, 2, 8]}
          size={2}
          speed={0.5}
          opacity={0.6}
          color="#FFD700"
        />
      )}

      {/* Underglow effect */}
      {customization.underglow && (
        <pointLight
          position={[0, -0.5, 0]}
          intensity={0.5}
          color="#00FFFF"
          distance={3}
        />
      )}

      {/* Main car body - Enhanced RX-3 */}
      <mesh castShadow receiveShadow>
        {/* Lower body/chassis */}
        <boxGeometry args={[4.2, 0.8, 1.8]} />
        <meshStandardMaterial 
          {...paintMaterial}
          envMapIntensity={1.5}
        />

        {/* Upper body/cabin */}
        <mesh position={[-0.2, 0.8, 0]} castShadow receiveShadow>
          <boxGeometry args={[3, 0.8, 1.6]} />
          <meshStandardMaterial 
            {...paintMaterial}
            envMapIntensity={1.5}
          />
        </mesh>

        {/* Front hood */}
        <mesh position={[1.4, 0.5, 0]} castShadow receiveShadow>
          <boxGeometry args={[1.4, 0.2, 1.7]} />
          <meshStandardMaterial 
            {...paintMaterial}
            envMapIntensity={1.5}
          />
        </mesh>

        {/* Trunk */}
        <mesh position={[-1.8, 0.5, 0]} castShadow receiveShadow>
          <boxGeometry args={[0.6, 0.2, 1.7]} />
          <meshStandardMaterial 
            {...paintMaterial}
            envMapIntensity={1.5}
          />
        </mesh>

        {/* Enhanced windshield with reflections */}
        <mesh position={[0.6, 1, 0]} castShadow rotation={[0.3, 0, 0]}>
          <planeGeometry args={[1.2, 0.8]} />
          <meshStandardMaterial 
            color="#87CEEB" 
            metalness={0.95} 
            roughness={0.05} 
            transparent 
            opacity={0.8}
            envMapIntensity={2}
          />
        </mesh>

        {/* Rear window */}
        <mesh position={[-1, 1, 0]} castShadow rotation={[-0.3, 0, 0]}>
          <planeGeometry args={[1, 0.7]} />
          <meshStandardMaterial 
            color="#87CEEB" 
            metalness={0.95} 
            roughness={0.05} 
            transparent 
            opacity={0.8}
            envMapIntensity={2}
          />
        </mesh>

        {/* Enhanced headlights with glow */}
        <mesh position={[2.1, 0.5, 0.6]} castShadow>
          <cylinderGeometry args={[0.25, 0.25, 0.1, 16]} />
          <meshStandardMaterial 
            color="#FFFFFF" 
            emissive="#FFFFAA" 
            emissiveIntensity={glowIntensity}
            metalness={0.8}
            roughness={0.1}
          />
        </mesh>
        <mesh position={[2.1, 0.5, -0.6]} castShadow>
          <cylinderGeometry args={[0.25, 0.25, 0.1, 16]} />
          <meshStandardMaterial 
            color="#FFFFFF" 
            emissive="#FFFFAA" 
            emissiveIntensity={glowIntensity}
            metalness={0.8}
            roughness={0.1}
          />
        </mesh>

        {/* Enhanced taillights */}
        <mesh position={[-2.1, 0.5, 0.6]} castShadow>
          <boxGeometry args={[0.1, 0.2, 0.3]} />
          <meshStandardMaterial 
            color="#8B0000" 
            emissive="#FF0000" 
            emissiveIntensity={glowIntensity * 0.8}
            metalness={0.7}
            roughness={0.2}
          />
        </mesh>
        <mesh position={[-2.1, 0.5, -0.6]} castShadow>
          <boxGeometry args={[0.1, 0.2, 0.3]} />
          <meshStandardMaterial 
            color="#8B0000" 
            emissive="#FF0000" 
            emissiveIntensity={glowIntensity * 0.8}
            metalness={0.7}
            roughness={0.2}
          />
        </mesh>

        {/* Chrome grille */}
        <mesh position={[2.1, 0.4, 0]} castShadow>
          <boxGeometry args={[0.1, 0.3, 1]} />
          <meshStandardMaterial 
            color="#C0C0C0" 
            metalness={1.0} 
            roughness={0.1}
            envMapIntensity={2}
          />
        </mesh>

        {/* Australian flag emblem */}
        <mesh position={[0, 1.2, 0]} rotation={[-Math.PI / 2, 0, 0]}>
          <circleGeometry args={[0.4, 5]} />
          <meshStandardMaterial 
            color="#FFFFFF" 
            metalness={0.8} 
            roughness={0.2}
          />
        </mesh>

        {/* Racing stripes */}
        {customization.hasRacingStripes && (
          <>
            <mesh position={[0, 0.81, 0]} castShadow>
              <boxGeometry args={[3, 0.01, 0.3]} />
              <meshStandardMaterial 
                color="#FFFFFF" 
                metalness={0.9} 
                roughness={0.1}
              />
            </mesh>
            <mesh position={[0, 0.81, 0]} castShadow>
              <boxGeometry args={[3, 0.01, 0.1]} />
              <meshStandardMaterial 
                color="#FF0000" 
                metalness={0.9} 
                roughness={0.1}
              />
            </mesh>
          </>
        )}

        {/* Spoiler */}
        {customization.spoiler && (
          <mesh position={[-1.8, 1.2, 0]} castShadow>
            <boxGeometry args={[0.4, 0.1, 1.4]} />
            <meshStandardMaterial 
              color="#1a1a1a" 
              metalness={0.8} 
              roughness={0.2}
            />
          </mesh>
        )}
      </mesh>

      {/* Enhanced wheels with better materials */}
      {[
        [1.4, -0.2, 0.9],
        [1.4, -0.2, -0.9],
        [-1.4, -0.2, 0.9],
        [-1.4, -0.2, -0.9]
      ].map((position, index) => (
        <group key={index} position={position as [number, number, number]}>
          <mesh castShadow receiveShadow scale={[wheelScale, wheelScale, 1]}>
            <cylinderGeometry args={[0.4, 0.4, 0.2, 24]} />
            <meshStandardMaterial 
              color="#1A1A1A" 
              metalness={0.7} 
              roughness={0.3}
              envMapIntensity={1.5}
            />
            {/* Rim */}
            <mesh position={[0, 0, 0]} castShadow>
              <cylinderGeometry args={[0.2, 0.2, 0.21, 24]} />
              <meshStandardMaterial 
                color="#C0C0C0" 
                metalness={1.0} 
                roughness={0.1}
                envMapIntensity={2}
              />
            </mesh>
          </mesh>
        </group>
      ))}

      {/* Exhaust smoke effect when revving */}
      {isRevving && (
        <group position={[-2.2, -0.3, 0]}>
          <Sparkles
            count={20}
            scale={[1, 1, 1]}
            size={1}
            speed={2}
            opacity={0.3}
            color="#666666"
          />
        </group>
      )}
    </group>
  );
};

// Camera controller for cinematic views
const CameraController = ({ preset }: { preset: string }) => {
  const { camera } = useThree();
  
  useEffect(() => {
    switch (preset) {
      case 'front':
        camera.position.set(8, 2, 0);
        break;
      case 'side':
        camera.position.set(0, 2, 8);
        break;
      case 'rear':
        camera.position.set(-8, 2, 0);
        break;
      case 'top':
        camera.position.set(0, 10, 0);
        break;
      case 'detail':
        camera.position.set(4, 1, 4);
        break;
      default:
        camera.position.set(8, 3, 8);
    }
    camera.lookAt(0, 0, 0);
  }, [preset, camera]);

  return null;
};

const Enhanced3DCarViewer: React.FC<Enhanced3DCarViewerProps> = ({
  className,
  isRevving = false,
  onRevEngine,
  customization = {},
  autoRotate = true,
  showControls = true,
  quality = 'high',
  environment = 'showroom'
}) => {
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [cameraPreset, setCameraPreset] = useState('default');
  const [showSettings, setShowSettings] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const getEnvironmentPreset = () => {
    switch (environment) {
      case 'garage': return 'warehouse';
      case 'showroom': return 'studio';
      case 'track': return 'city';
      case 'sunset': return 'sunset';
      case 'city': return 'night';
      default: return 'studio';
    }
  };

  const getQualitySettings = () => {
    switch (quality) {
      case 'low': return { dpr: [0.5, 1], shadows: false };
      case 'medium': return { dpr: [1, 1.5], shadows: true };
      case 'high': return { dpr: [1, 2], shadows: true };
      case 'ultra': return { dpr: [2, 3], shadows: true };
      default: return { dpr: [1, 2], shadows: true };
    }
  };

  const qualitySettings = getQualitySettings();

  return (
    <div className={`relative w-full h-[500px] ${className} overflow-hidden rounded-lg`}>
      <Canvas 
        shadows={qualitySettings.shadows} 
        dpr={qualitySettings.dpr}
        gl={{ antialias: true, alpha: false }}
      >
        <color attach="background" args={['#0a0a0a']} />
        <fog attach="fog" args={['#0a0a0a', 10, 50]} />
        
        <PerspectiveCamera makeDefault position={[8, 3, 8]} fov={45} />
        <CameraController preset={cameraPreset} />
        
        {/* Enhanced lighting setup */}
        <ambientLight intensity={0.4} />
        <directionalLight 
          position={[10, 10, 5]} 
          intensity={1} 
          castShadow 
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
        <pointLight position={[-10, 5, -10]} intensity={0.5} color="#4169E1" />
        <spotLight 
          position={[0, 10, 0]} 
          angle={0.3} 
          penumbra={1} 
          intensity={0.8} 
          castShadow
          color="#FFD700"
        />

        <EnhancedRX3Model 
          isRevving={isRevving} 
          customization={customization}
          autoRotate={autoRotate}
        />

        {/* Reflective floor */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -1.5, 0]} receiveShadow>
          <planeGeometry args={[20, 20]} />
          <MeshReflectorMaterial
            blur={[300, 100]}
            resolution={1024}
            mixBlur={1}
            mixStrength={40}
            roughness={1}
            depthScale={1.2}
            minDepthThreshold={0.4}
            maxDepthThreshold={1.4}
            color="#050505"
            metalness={0.5}
          />
        </mesh>

        <Environment preset={getEnvironmentPreset()} />
        
        <OrbitControls
          enablePan={false}
          enableZoom={true}
          minPolarAngle={Math.PI / 6}
          maxPolarAngle={Math.PI / 2}
          autoRotate={autoRotate}
          autoRotateSpeed={0.5}
        />
      </Canvas>

      {/* Enhanced Controls */}
      {showControls && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Top Controls */}
          <div className="absolute top-4 left-4 right-4 flex justify-between pointer-events-auto">
            <div className="flex gap-2">
              <Badge className="bg-black/60 text-white border-racing-yellow/30">
                {environment.charAt(0).toUpperCase() + environment.slice(1)}
              </Badge>
              <Badge className="bg-black/60 text-white border-racing-yellow/30">
                {quality.toUpperCase()}
              </Badge>
            </div>
            
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                className="bg-black/60 border-racing-yellow/30 text-white hover:bg-racing-yellow hover:text-black"
                onClick={() => setIsFullscreen(!isFullscreen)}
              >
                <Maximize className="w-4 h-4" />
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                className="bg-black/60 border-racing-yellow/30 text-white hover:bg-racing-yellow hover:text-black"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Camera Presets */}
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 pointer-events-auto">
            <div className="flex gap-1 bg-black/60 rounded-lg p-1">
              {['default', 'front', 'side', 'rear', 'top', 'detail'].map((preset) => (
                <Button
                  key={preset}
                  size="sm"
                  variant={cameraPreset === preset ? "default" : "ghost"}
                  className="text-xs"
                  onClick={() => setCameraPreset(preset)}
                >
                  <Camera className="w-3 h-3 mr-1" />
                  {preset}
                </Button>
              ))}
            </div>
          </div>

          {/* Bottom Controls */}
          <div className="absolute bottom-4 left-4 right-4 flex justify-between items-end pointer-events-auto">
            <div className="bg-black/60 text-white px-3 py-2 rounded-lg text-sm">
              <div className="flex items-center gap-2 mb-1">
                <Eye className="w-4 h-4" />
                <span>Controls</span>
              </div>
              <div className="text-xs text-gray-300">
                Click & drag to rotate • Scroll to zoom • Right-click to pan
              </div>
            </div>

            <div className="flex gap-2">
              {onRevEngine && (
                <Button
                  size="sm"
                  className={`${isRevving 
                    ? 'bg-racing-red hover:bg-red-700' 
                    : 'bg-racing-yellow hover:bg-yellow-600 text-black'
                  }`}
                  onClick={onRevEngine}
                >
                  {isRevving ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
                  {isRevving ? 'Stop Engine' : 'Rev Engine'}
                </Button>
              )}
              
              <Button
                size="sm"
                variant="outline"
                className="bg-black/60 border-racing-yellow/30 text-white hover:bg-racing-yellow hover:text-black"
                onClick={() => setSoundEnabled(!soundEnabled)}
              >
                {soundEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
              </Button>
            </div>
          </div>

          {/* Settings Panel */}
          <AnimatePresence>
            {showSettings && (
              <motion.div
                initial={{ opacity: 0, x: 300 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 300 }}
                className="absolute top-16 right-4 bg-black/90 backdrop-blur-sm border border-racing-yellow/30 rounded-lg p-4 w-64 pointer-events-auto"
              >
                <h3 className="text-white font-semibold mb-3">Viewer Settings</h3>
                
                <div className="space-y-3">
                  <div>
                    <label className="text-gray-300 text-sm">Environment</label>
                    <select 
                      className="w-full mt-1 bg-gray-800 border border-gray-600 rounded px-2 py-1 text-white text-sm"
                      value={environment}
                      onChange={(e) => {/* Handle environment change */}}
                    >
                      <option value="garage">Garage</option>
                      <option value="showroom">Showroom</option>
                      <option value="track">Track</option>
                      <option value="sunset">Sunset</option>
                      <option value="city">City</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="text-gray-300 text-sm">Quality</label>
                    <select 
                      className="w-full mt-1 bg-gray-800 border border-gray-600 rounded px-2 py-1 text-white text-sm"
                      value={quality}
                      onChange={(e) => {/* Handle quality change */}}
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="ultra">Ultra</option>
                    </select>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}

      {/* Revving effects overlay */}
      {isRevving && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-32 h-16 bg-gradient-to-t from-gray-600/30 to-transparent rounded-full animate-pulse" />
        </div>
      )}
    </div>
  );
};

export default Enhanced3DCarViewer;
