
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { useIsMobile } from '@/hooks/use-mobile';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { User, LogOut, Settings, Trophy, Crown } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import LoginForm from '@/components/auth/LoginForm';

const Navbar = () => {
  const isMobile = useIsMobile();
  const { user, isAuthenticated, logout } = useAuth();
  const [showLoginDialog, setShowLoginDialog] = useState(false);

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Play Game', path: '/play' },
    { name: 'Garage', path: '/garage' },
    { name: 'Events', path: '/events' },
    { name: 'Leaderboard', path: '/leaderboard' },
  ];

  const renderNavItems = () => (
    <>
      {navItems.map((item) => (
        <li key={item.name}>
          <Link
            to={item.path}
            className="nav-link cursor-pointer"
          >
            {item.name}
          </Link>
        </li>
      ))}
    </>
  );

  return (
    <nav className="sticky top-0 z-50 bg-background border-b border-racing-yellow/20 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center gap-4">
            {/* Logo */}
            <Link to="/" className="flex-shrink-0 cursor-pointer">
              <span className="text-racing-yellow font-bold text-2xl">AUSSIE</span>
              <span className="text-racing-red font-bold text-2xl">BURNOUT</span>
              <span className="text-racing-yellow font-bold text-2xl">LEGENDS</span>
            </Link>
          </div>

          {isMobile ? (
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" className="p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                  </svg>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="bg-background">
                <div className="mt-6 flow-root">
                  <ul className="flex flex-col space-y-4">{renderNavItems()}</ul>
                  <div className="mt-8">
                    {isAuthenticated ? (
                      <div className="space-y-4">
                        <div className="flex items-center gap-2 p-2 bg-racing-yellow/10 rounded">
                          {user?.foundersEdition && <Crown className="w-4 h-4 text-racing-yellow" />}
                          <span className="text-white font-medium">{user?.username}</span>
                        </div>
                        <Button
                          onClick={logout}
                          variant="outline"
                          className="w-full"
                        >
                          <LogOut className="w-4 h-4 mr-2" />
                          Logout
                        </Button>
                      </div>
                    ) : (
                      <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
                        <DialogTrigger asChild>
                          <Button className="bg-racing-red hover:bg-red-700 text-white w-full">
                            Login / Register
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-md">
                          <LoginForm onSuccess={() => setShowLoginDialog(false)} />
                        </DialogContent>
                      </Dialog>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          ) : (
            <div className="hidden md:flex items-center space-x-4">
              <ul className="flex items-center space-x-2">
                {renderNavItems()}
              </ul>
              <div className="ml-4">
                {isAuthenticated ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="flex items-center gap-2">
                        {user?.foundersEdition && <Crown className="w-4 h-4 text-racing-yellow" />}
                        <User className="w-4 h-4" />
                        <span>{user?.username}</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      <DropdownMenuItem asChild>
                        <Link to="/garage" className="flex items-center">
                          <Settings className="w-4 h-4 mr-2" />
                          Garage
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Trophy className="w-4 h-4 mr-2" />
                        Achievements
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={logout}>
                        <LogOut className="w-4 h-4 mr-2" />
                        Logout
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
                    <DialogTrigger asChild>
                      <Button className="bg-racing-red hover:bg-red-700 text-white">
                        Login / Register
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <LoginForm onSuccess={() => setShowLoginDialog(false)} />
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
