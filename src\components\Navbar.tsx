
import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { useIsMobile } from '@/hooks/use-mobile';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';

const Navbar = () => {
  const isMobile = useIsMobile();

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Play Game', path: '/play' },
    { name: 'Garage', path: '/garage' },
    { name: 'Events', path: '/events' },
    { name: 'Leaderboard', path: '/leaderboard' },
  ];

  const renderNavItems = () => (
    <>
      {navItems.map((item) => (
        <li key={item.name}>
          <Link
            to={item.path}
            className="nav-link cursor-pointer"
          >
            {item.name}
          </Link>
        </li>
      ))}
    </>
  );

  return (
    <nav className="sticky top-0 z-50 bg-background border-b border-racing-yellow/20 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center gap-4">
            {/* Logo */}
            <Link to="/" className="flex-shrink-0 cursor-pointer">
              <span className="text-racing-yellow font-bold text-2xl">AUSSIE</span>
              <span className="text-racing-red font-bold text-2xl">BURNOUT</span>
              <span className="text-racing-yellow font-bold text-2xl">LEGENDS</span>
            </Link>
          </div>

          {isMobile ? (
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" className="p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                  </svg>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="bg-background">
                <div className="mt-6 flow-root">
                  <ul className="flex flex-col space-y-4">{renderNavItems()}</ul>
                  <div className="mt-8">
                    <Link to="/register">
                      <Button
                        className="bg-racing-red hover:bg-red-700 text-white w-full"
                      >
                        Register Now
                      </Button>
                    </Link>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          ) : (
            <div className="hidden md:flex items-center space-x-4">
              <ul className="flex items-center space-x-2">
                {renderNavItems()}
              </ul>
              <div className="ml-4">
                <Link to="/register">
                  <Button
                    className="bg-racing-red hover:bg-red-700 text-white"
                  >
                    Register Now
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
