import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Car, Zap, Gauge, Wrench, DollarSign, Star, Trophy } from 'lucide-react';
import AustralianEmblem from '@/components/AustralianEmblem';

interface CarSpec {
  id: string;
  name: string;
  model: string;
  year: string;
  image?: string;
  stats: {
    power: number;
    handling: number;
    acceleration: number;
    braking: number;
    topSpeed: number;
    weight: number;
  };
  engine: {
    type: string;
    displacement: string;
    cylinders: string;
    fuel: string;
  };
  performance: {
    zeroToHundred: number;
    quarterMile: number;
    topSpeed: number;
  };
  price: {
    original: number;
    current: number;
  };
  rarity: 'common' | 'uncommon' | 'rare' | 'legendary';
  pros: string[];
  cons: string[];
  description: string;
}

const CAR_DATABASE: CarSpec[] = [
  {
    id: 'rx3-founders',
    name: 'Mazda RX-3 Savanna (Founders Edition)',
    model: 'RX-3',
    year: '1973',
    stats: {
      power: 85,
      handling: 75,
      acceleration: 80,
      braking: 70,
      topSpeed: 180,
      weight: 1000
    },
    engine: {
      type: 'Rotary',
      displacement: '1.1L (12A)',
      cylinders: '2 Rotor',
      fuel: 'Petrol'
    },
    performance: {
      zeroToHundred: 8.5,
      quarterMile: 16.2,
      topSpeed: 180
    },
    price: {
      original: 3500,
      current: 25000
    },
    rarity: 'legendary',
    pros: [
      'Unique rotary engine sound',
      'Lightweight and balanced',
      'Strong motorsport heritage',
      'Excellent modification potential'
    ],
    cons: [
      'Higher fuel consumption',
      'Requires specialized maintenance',
      'Parts can be expensive',
      'Emissions concerns'
    ],
    description: 'The legendary RX-3 with its distinctive rotary engine became an icon of Australian motorsport.'
  },
  {
    id: 'holden-commodore',
    name: 'Holden Commodore VL Turbo',
    model: 'Commodore VL',
    year: '1986',
    stats: {
      power: 95,
      handling: 85,
      acceleration: 90,
      braking: 80,
      topSpeed: 210,
      weight: 1400
    },
    engine: {
      type: 'Inline-6 Turbo',
      displacement: '3.0L (RB30ET)',
      cylinders: '6',
      fuel: 'Petrol'
    },
    performance: {
      zeroToHundred: 7.1,
      quarterMile: 15.1,
      topSpeed: 210
    },
    price: {
      original: 18000,
      current: 35000
    },
    rarity: 'rare',
    pros: [
      'Powerful turbocharged engine',
      'Excellent straight-line performance',
      'Practical family car',
      'Strong aftermarket support'
    ],
    cons: [
      'Turbo lag in stock form',
      'Heavier than competitors',
      'Fuel consumption under boost',
      'Complexity of turbo system'
    ],
    description: 'The VL Turbo brought forced induction to the masses and became a street machine favorite.'
  },
  {
    id: 'ford-falcon',
    name: 'Ford Falcon XB GT',
    model: 'Falcon XB',
    year: '1973',
    stats: {
      power: 100,
      handling: 80,
      acceleration: 95,
      braking: 75,
      topSpeed: 200,
      weight: 1500
    },
    engine: {
      type: 'V8',
      displacement: '5.8L (351 Cleveland)',
      cylinders: '8',
      fuel: 'Petrol'
    },
    performance: {
      zeroToHundred: 6.5,
      quarterMile: 14.8,
      topSpeed: 200
    },
    price: {
      original: 4200,
      current: 80000
    },
    rarity: 'legendary',
    pros: [
      'Iconic V8 sound and power',
      'Excellent straight-line performance',
      'Strong build quality',
      'Legendary status'
    ],
    cons: [
      'Heavy fuel consumption',
      'Large and heavy',
      'Expensive to maintain',
      'Limited handling compared to modern cars'
    ],
    description: 'The XB GT is an Australian muscle car legend, immortalized in Mad Max.'
  },
  {
    id: 'torana-xu1',
    name: 'Holden Torana XU-1',
    model: 'Torana XU-1',
    year: '1970',
    stats: {
      power: 80,
      handling: 90,
      acceleration: 85,
      braking: 85,
      topSpeed: 175,
      weight: 950
    },
    engine: {
      type: 'Inline-6',
      displacement: '3.3L (202)',
      cylinders: '6',
      fuel: 'Petrol'
    },
    performance: {
      zeroToHundred: 8.8,
      quarterMile: 16.5,
      topSpeed: 175
    },
    price: {
      original: 2800,
      current: 45000
    },
    rarity: 'rare',
    pros: [
      'Excellent handling and balance',
      'Lightweight construction',
      'Strong racing pedigree',
      'Distinctive styling'
    ],
    cons: [
      'Limited power in stock form',
      'Smaller interior space',
      'Parts becoming scarce',
      'Rust issues in some examples'
    ],
    description: 'The XU-1 was Holden\'s answer to the need for a lightweight, agile performance car.'
  }
];

interface CarComparisonProps {
  className?: string;
}

const CarComparison: React.FC<CarComparisonProps> = ({ className }) => {
  const [selectedCars, setSelectedCars] = useState<string[]>([]);
  const [comparisonMode, setComparisonMode] = useState<'stats' | 'specs' | 'performance'>('stats');

  const handleCarSelect = (carId: string, slot: number) => {
    const newSelection = [...selectedCars];
    newSelection[slot] = carId;
    setSelectedCars(newSelection);
  };

  const getSelectedCarData = (slot: number): CarSpec | null => {
    const carId = selectedCars[slot];
    return carId ? CAR_DATABASE.find(car => car.id === carId) || null : null;
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-500';
      case 'uncommon': return 'bg-green-500';
      case 'rare': return 'bg-blue-500';
      case 'legendary': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatColor = (value: number) => {
    if (value >= 90) return 'bg-green-500';
    if (value >= 70) return 'bg-yellow-500';
    if (value >= 50) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const renderCarSelector = (slot: number) => (
    <div className="space-y-4">
      <Select onValueChange={(value) => handleCarSelect(value, slot)}>
        <SelectTrigger className="bg-gray-800 border-gray-700">
          <SelectValue placeholder={`Select Car ${slot + 1}`} />
        </SelectTrigger>
        <SelectContent>
          {CAR_DATABASE.map((car) => (
            <SelectItem key={car.id} value={car.id}>
              {car.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {getSelectedCarData(slot) && (
        <Card className="bg-gray-900/40 border-gray-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <Car className="w-8 h-8 text-racing-yellow" />
              <div>
                <h3 className="font-semibold text-white">{getSelectedCarData(slot)!.name}</h3>
                <p className="text-sm text-gray-400">{getSelectedCarData(slot)!.year}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2 mb-3">
              <Badge className={`${getRarityColor(getSelectedCarData(slot)!.rarity)} text-white text-xs`}>
                {getSelectedCarData(slot)!.rarity}
              </Badge>
              <div className="text-xs text-gray-400">
                ${getSelectedCarData(slot)!.price.current.toLocaleString()}
              </div>
            </div>
            
            <p className="text-xs text-gray-400">{getSelectedCarData(slot)!.description}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderComparison = () => {
    const car1 = getSelectedCarData(0);
    const car2 = getSelectedCarData(1);
    
    if (!car1 || !car2) {
      return (
        <div className="text-center py-12">
          <Car className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl text-white mb-2">Select Two Cars</h3>
          <p className="text-gray-400">Choose two cars to compare their specifications and performance.</p>
        </div>
      );
    }

    if (comparisonMode === 'stats') {
      return (
        <div className="space-y-6">
          <h3 className="text-xl font-semibold text-white text-center">Performance Statistics</h3>
          
          <div className="grid grid-cols-1 gap-6">
            {Object.entries(car1.stats).map(([stat, value1]) => {
              const value2 = car2.stats[stat as keyof typeof car2.stats];
              const max = Math.max(value1, value2, 100);
              
              return (
                <div key={stat} className="space-y-2">
                  <h4 className="text-white font-medium capitalize">{stat.replace(/([A-Z])/g, ' $1')}</h4>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">{car1.name}</span>
                        <span className="text-white">{value1}</span>
                      </div>
                      <Progress value={(value1 / max) * 100} className="h-3" />
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">{car2.name}</span>
                        <span className="text-white">{value2}</span>
                      </div>
                      <Progress value={(value2 / max) * 100} className="h-3" />
                    </div>
                  </div>
                  
                  <div className="text-center">
                    {value1 > value2 ? (
                      <Badge className="bg-green-500 text-white text-xs">
                        {car1.model} wins by {value1 - value2}
                      </Badge>
                    ) : value2 > value1 ? (
                      <Badge className="bg-blue-500 text-white text-xs">
                        {car2.model} wins by {value2 - value1}
                      </Badge>
                    ) : (
                      <Badge className="bg-gray-500 text-white text-xs">Tie</Badge>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      );
    }

    if (comparisonMode === 'specs') {
      return (
        <div className="space-y-6">
          <h3 className="text-xl font-semibold text-white text-center">Technical Specifications</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gray-900/40 border-gray-700">
              <CardHeader>
                <CardTitle className="text-racing-yellow">{car1.name}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="text-white font-medium mb-2">Engine</h4>
                  <div className="text-sm text-gray-300 space-y-1">
                    <div>Type: {car1.engine.type}</div>
                    <div>Displacement: {car1.engine.displacement}</div>
                    <div>Configuration: {car1.engine.cylinders}</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-white font-medium mb-2">Performance</h4>
                  <div className="text-sm text-gray-300 space-y-1">
                    <div>0-100 km/h: {car1.performance.zeroToHundred}s</div>
                    <div>Quarter Mile: {car1.performance.quarterMile}s</div>
                    <div>Top Speed: {car1.performance.topSpeed} km/h</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-white font-medium mb-2">Pros & Cons</h4>
                  <div className="space-y-2">
                    <div>
                      <div className="text-green-400 text-xs font-medium mb-1">Pros:</div>
                      <ul className="text-xs text-gray-300 space-y-1">
                        {car1.pros.slice(0, 2).map((pro, index) => (
                          <li key={index}>• {pro}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <div className="text-red-400 text-xs font-medium mb-1">Cons:</div>
                      <ul className="text-xs text-gray-300 space-y-1">
                        {car1.cons.slice(0, 2).map((con, index) => (
                          <li key={index}>• {con}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-gray-900/40 border-gray-700">
              <CardHeader>
                <CardTitle className="text-racing-yellow">{car2.name}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="text-white font-medium mb-2">Engine</h4>
                  <div className="text-sm text-gray-300 space-y-1">
                    <div>Type: {car2.engine.type}</div>
                    <div>Displacement: {car2.engine.displacement}</div>
                    <div>Configuration: {car2.engine.cylinders}</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-white font-medium mb-2">Performance</h4>
                  <div className="text-sm text-gray-300 space-y-1">
                    <div>0-100 km/h: {car2.performance.zeroToHundred}s</div>
                    <div>Quarter Mile: {car2.performance.quarterMile}s</div>
                    <div>Top Speed: {car2.performance.topSpeed} km/h</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-white font-medium mb-2">Pros & Cons</h4>
                  <div className="space-y-2">
                    <div>
                      <div className="text-green-400 text-xs font-medium mb-1">Pros:</div>
                      <ul className="text-xs text-gray-300 space-y-1">
                        {car2.pros.slice(0, 2).map((pro, index) => (
                          <li key={index}>• {pro}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <div className="text-red-400 text-xs font-medium mb-1">Cons:</div>
                      <ul className="text-xs text-gray-300 space-y-1">
                        {car2.cons.slice(0, 2).map((con, index) => (
                          <li key={index}>• {con}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <div className={`${className}`}>
      <Card className="bg-black/40 border-racing-yellow/20">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl text-racing-yellow">Car Comparison Tool</CardTitle>
              <CardDescription className="text-gray-400">
                Compare specifications and performance of different cars
              </CardDescription>
            </div>
            <AustralianEmblem size="sm" className="opacity-70" />
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {renderCarSelector(0)}
            {renderCarSelector(1)}
          </div>
          
          {selectedCars[0] && selectedCars[1] && (
            <div className="space-y-4">
              <div className="flex justify-center">
                <div className="flex bg-gray-800 rounded-lg p-1">
                  <Button
                    variant={comparisonMode === 'stats' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setComparisonMode('stats')}
                    className="flex items-center gap-2"
                  >
                    <Gauge className="w-4 h-4" />
                    Stats
                  </Button>
                  <Button
                    variant={comparisonMode === 'specs' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setComparisonMode('specs')}
                    className="flex items-center gap-2"
                  >
                    <Wrench className="w-4 h-4" />
                    Specs
                  </Button>
                </div>
              </div>
              
              <div className="border-t border-gray-700 pt-6">
                {renderComparison()}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CarComparison;
