import Link from "next/link";
import { useRouter } from "next/router";

const navLinks = [
  { name: "Home", href: "/" },
  { name: "Feed", href: "/feed" }
];

export default function Navbar() {
  const router = useRouter();

  return (
    <nav className="sticky-nav flex items-center justify-between px-8 py-4 mb-8">
      <div className="flex items-center gap-2">
        <span className="text-primary font-bold text-2xl tracking-tight">Socially</span>
        <span className="ml-2 text-accent font-semibold text-sm bg-accent/10 px-2 py-1 rounded">Beta</span>
      </div>
      <div className="flex gap-6">
        {navLinks.map(link => (
          <Link key={link.href} href={link.href} passHref legacyBehavior>
            <a
              className={`font-medium transition-colors duration-200 ${
                router.pathname === link.href
                  ? "text-primary underline underline-offset-4"
                  : "text-gray-700 hover:text-primary"
              }`}
              tabIndex={0}
            >
              {link.name}
            </a>
          </Link>
        ))}
      </div>
    </nav>
  );
}
