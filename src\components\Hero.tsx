
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";

const Hero = () => {
  const { toast } = useToast();
  const [remainingFounders, setRemainingFounders] = useState(100);
  const [isRevving, setIsRevving] = useState(false);

  useEffect(() => {
    // For demo purposes, just decrease the count randomly
    const randomCount = Math.floor(Math.random() * 40);
    setRemainingFounders(100 - randomCount);
  }, []);

  const handleRevEngine = () => {
    setIsRevving(true);

    toast({
      title: "Engine Revving!",
      description: "Feel the power of the iconic RX-3's rotary engine!",
      className: "bg-racing-black border-racing-yellow text-white",
    });

    setTimeout(() => setIsRevving(false), 3000);
  };

  return (
    <div className="relative overflow-hidden">
      {/* Checkerboard pattern at top */}
      <div className="absolute top-0 left-0 right-0 h-4 checkerboard-flag"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="pt-20 pb-16 md:pt-24 md:pb-20 flex flex-col md:flex-row items-center">
          {/* Hero content */}
          <div className="md:w-1/2 mb-10 md:mb-0 space-y-6">
            <div className="relative bg-[#00008B] p-6 rounded-lg"> {/* Australian flag dark blue background */}
              <h1 className="text-5xl md:text-6xl font-bold tracking-tight">
                <span className="text-white block">AUSSIE</span>
                <span className="text-[#FF0000] block">BURNOUT</span>
                <span className="text-white block">LEGENDS</span>
              </h1>
              {/* Southern Cross stars */}
              <div className="absolute top-3 right-3 flex space-x-1">
                <div className="w-3 h-3 bg-white transform rotate-45"></div>
                <div className="w-2 h-2 bg-white transform rotate-45"></div>
                <div className="w-4 h-4 bg-white transform rotate-45"></div>
                <div className="w-2 h-2 bg-white transform rotate-45"></div>
              </div>
            </div>

            <p className="text-xl text-gray-300 max-w-prose">
              Experience Australia's iconic car culture with authentic burnouts, drag races, and more.
              Claim your exclusive Founders Edition Mazda RX-3 Savanna now!
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                asChild
                className="bg-[#FF0000] hover:bg-red-700 text-white font-bold py-3 px-6 rounded shadow-lg transform transition duration-300 hover:scale-105 hover:shadow-xl uppercase tracking-wider"
              >
                <Link to="/register">Register Now</Link>
              </Button>
              <Button
                asChild
                className="bg-[#00008B] hover:bg-blue-800 text-white font-bold py-3 px-6 rounded shadow-lg transform transition duration-300 hover:scale-105 hover:shadow-xl uppercase tracking-wider"
              >
                <Link to="/play">Play Game</Link>
              </Button>
              <Button
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-[#00008B] transition-colors duration-300 py-3 px-6"
                onClick={handleRevEngine}
              >
                Rev Engine
              </Button>
            </div>
            <div className="bg-[#00008B]/50 p-4 rounded-lg border border-white/20 backdrop-blur-sm">
              <p className="text-white font-medium">
                <span className="text-[#FF0000] font-bold">{remainingFounders}</span> Founders Edition cars remaining!
              </p>
              <p className="text-sm text-gray-400">
                The first 100 players get an exclusive Mazda RX-3 Savanna with the "Founders Edition" livery.
              </p>
            </div>
          </div>

          {/* Hero image */}
          <div className="md:w-1/2 relative">
            <div className={`relative flex justify-center ${isRevving ? 'animate-rev-engine' : ''}`}>
              <img
                src="/lovable-uploads/20adddf2-9fe4-455f-8e10-8f4773d69892.png"
                alt="Mazda RX-3 Savanna Racing Edition"
                className="rounded-lg shadow-2xl transform rotate-6 max-w-full"
              />
              <div className="bg-gradient-to-r from-[#00008B] via-[#FF0000] to-white text-black font-bold py-1 px-3 rounded-full text-sm uppercase tracking-wide shadow-lg transform rotate-[-5deg] absolute top-4 right-4">
                Founders Edition
              </div>
              {isRevving && (
                <div className="smoke-animation absolute -bottom-10"></div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tire tracks */}
      <div className="absolute bottom-0 left-0 right-0 h-8 opacity-10 tire-track"></div>

      {/* Checkerboard pattern at bottom */}
      <div className="absolute bottom-0 left-0 right-0 h-4 checkerboard-flag"></div>
    </div>
  );
};

export default Hero;
