import Navbar from "../components/Navbar";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen feed-bg">
      <Navbar />
      <main className="max-w-2xl mx-auto flex flex-col items-center justify-center py-24 px-4">
        <h1 className="text-5xl font-extrabold text-primary mb-4 text-center drop-shadow">
          Welcome to <span className="text-accent">Socially</span>
        </h1>
        <p className="text-lg text-gray-600 mb-8 text-center max-w-xl">
          A modern, clean social media feed built with Next.js and Tailwind CSS.<br />
          Explore posts, connect with others, and enjoy a beautiful UI inspired by Twitter and Dribbble.
        </p>
        <Link href="/feed" passHref legacyBehavior>
          <a className="btn-primary text-lg px-8 py-3">Go to Feed</a>
        </Link>
      </main>
    </div>
  );
}
