
# Aussie Burnout Legends: Developer Guidelines

This document provides guidelines and best practices for developers working on the Aussie Burnout Legends project.

## Project Architecture

### Overall Structure
- **Frontend**: React with TypeScript, using Tailwind CSS for styling
- **3D Rendering**: Three.js with React Three Fiber
- **State Management**: React Context API for global state, local state for component-specific data
- **Routing**: React Router for navigation
- **UI Components**: Custom components following Australian theme guidelines

### Directory Structure
```
src/
├── components/          # Reusable UI components
│   ├── 3D/              # 3D model components
│   └── ui/              # UI components from shadcn/ui
├── hooks/               # Custom React hooks
├── context/             # React context providers
├── pages/               # Page components
├── lib/                 # Utility functions
├── docs/                # Project documentation
└── integrations/        # External service integrations
```

## Design Guidelines

### Color Palette
The application follows the Australian flag color scheme:
- Navy Blue: `#00008B` - Primary background, headers
- Red: `#FF0000` - Accents, buttons, highlights
- White: `#FFFFFF` - Text, borders, secondary elements

### Typography
- Headings: Sans-serif, bold
- Body text: Sans-serif, regular
- Racing-themed elements: Use uppercase, tracking-wider

### Component Guidelines
- All components should be responsive
- Use the provided Australian color theme consistently
- Ensure accessibility with proper contrast ratios
- Follow atomic design principles where possible

## 3D Implementation Guidelines

### Car Models
- Use the `CarModelUtils.tsx` for constants and helper functions
- Follow the established scale and measurement system
- Ensure low-poly models for web performance
- Use Australian themes in vehicle customization

### Performance Optimization
- Implement level-of-detail (LOD) for complex models
- Use instancing for repeated elements
- Optimize meshes and textures for web delivery
- Implement proper loading states during model initialization

## Adding New Features

### Planning
1. Consult the `GameFeatureRoadmap.md` for prioritization
2. Create mockups for UI elements
3. Plan data structures and component interactions
4. Consider performance implications

### Implementation
1. Create new components in appropriate directories
2. Add necessary state management
3. Implement 3D elements if required
4. Add routing if creating new pages
5. Update documentation to reflect new features

### Testing
1. Test on multiple screen sizes
2. Verify against the Australian theme guidelines
3. Check performance on both desktop and mobile
4. Verify all interactive elements function correctly

## Code Style and Best Practices

### TypeScript
- Use proper typing for all variables and functions
- Leverage interfaces and types for complex structures
- Avoid using `any` type when possible
- Document complex types with comments

### React
- Use functional components with hooks
- Break down large components into smaller, focused ones
- Use proper key props in lists
- Implement proper error handling
- Use React.memo for performance optimization when appropriate

### Naming Conventions
- PascalCase for component files and React components
- camelCase for variables, functions, and instances
- Use descriptive names that reflect purpose

## Deployment and Release Process

### Pre-Deployment Checklist
- Verify against `DeploymentChecklist.md`
- Run all tests
- Check for console errors
- Verify responsive design
- Ensure all features are documented

### Version Control
- Follow semantic versioning (MAJOR.MINOR.PATCH)
- Create descriptive commit messages
- Use feature branches for new development

### Continuous Integration
- Automated builds triggered on push
- Linting and type checking
- Bundle size analysis

## Performance Budgets

- Initial load time: < 3 seconds on 4G connection
- Time to Interactive: < 5 seconds
- Total bundle size: < 500KB (compressed)
- 3D model polygon count: < 10k per model
- Frame rate: Maintain 30+ FPS on mid-range devices

## Resources and References

- Three.js documentation: https://threejs.org/docs/
- React Three Fiber: https://docs.pmnd.rs/react-three-fiber
- Australian Design System: https://designsystem.gov.au/
- Australian Car Culture Reference: Various Australian automotive magazines and websites
