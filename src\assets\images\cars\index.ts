// High-quality car images and assets
export const carImages = {
  // Mazda RX-3 Savanna
  rx3: {
    main: '/images/cars/rx3-main.jpg',
    profile: '/images/cars/rx3-profile.jpg',
    front: '/images/cars/rx3-front.jpg',
    rear: '/images/cars/rx3-rear.jpg',
    interior: '/images/cars/rx3-interior.jpg',
    engine: '/images/cars/rx3-engine.jpg',
    founders: '/images/cars/rx3-founders-edition.jpg',
    thumbnail: '/images/cars/rx3-thumb.jpg'
  },
  
  // Holden Commodore VL Turbo
  commodore: {
    main: '/images/cars/commodore-main.jpg',
    profile: '/images/cars/commodore-profile.jpg',
    front: '/images/cars/commodore-front.jpg',
    rear: '/images/cars/commodore-rear.jpg',
    interior: '/images/cars/commodore-interior.jpg',
    engine: '/images/cars/commodore-engine.jpg',
    thumbnail: '/images/cars/commodore-thumb.jpg'
  },
  
  // Ford Falcon XB GT
  falcon: {
    main: '/images/cars/falcon-main.jpg',
    profile: '/images/cars/falcon-profile.jpg',
    front: '/images/cars/falcon-front.jpg',
    rear: '/images/cars/falcon-rear.jpg',
    interior: '/images/cars/falcon-interior.jpg',
    engine: '/images/cars/falcon-engine.jpg',
    thumbnail: '/images/cars/falcon-thumb.jpg'
  },
  
  // Holden Torana XU-1
  torana: {
    main: '/images/cars/torana-main.jpg',
    profile: '/images/cars/torana-profile.jpg',
    front: '/images/cars/torana-front.jpg',
    rear: '/images/cars/torana-rear.jpg',
    interior: '/images/cars/torana-interior.jpg',
    engine: '/images/cars/torana-engine.jpg',
    thumbnail: '/images/cars/torana-thumb.jpg'
  }
};

// Event and location images
export const eventImages = {
  burnout: {
    championship: '/images/events/burnout-championship.jpg',
    smoke: '/images/events/burnout-smoke.jpg',
    crowd: '/images/events/burnout-crowd.jpg'
  },
  drift: {
    series: '/images/events/drift-series.jpg',
    action: '/images/events/drift-action.jpg',
    track: '/images/events/drift-track.jpg'
  },
  race: {
    circuit: '/images/events/race-circuit.jpg',
    start: '/images/events/race-start.jpg',
    finish: '/images/events/race-finish.jpg'
  },
  show: {
    display: '/images/events/car-show.jpg',
    judging: '/images/events/car-judging.jpg',
    awards: '/images/events/car-awards.jpg'
  }
};

// Background and environment images
export const backgroundImages = {
  hero: '/images/backgrounds/hero-bg.jpg',
  garage: '/images/backgrounds/garage-bg.jpg',
  track: '/images/backgrounds/track-bg.jpg',
  workshop: '/images/backgrounds/workshop-bg.jpg',
  outback: '/images/backgrounds/outback-bg.jpg',
  city: '/images/backgrounds/city-bg.jpg'
};

// UI and icon images
export const uiImages = {
  logo: '/images/ui/logo.png',
  logoText: '/images/ui/logo-text.png',
  australianFlag: '/images/ui/australian-flag.png',
  foundersEdition: '/images/ui/founders-edition-badge.png',
  achievements: {
    welcome: '/images/achievements/welcome.png',
    firstBurnout: '/images/achievements/first-burnout.png',
    firstDrift: '/images/achievements/first-drift.png',
    speedDemon: '/images/achievements/speed-demon.png',
    styleMaster: '/images/achievements/style-master.png',
    perfectionist: '/images/achievements/perfectionist.png',
    legend: '/images/achievements/legend.png'
  },
  trophies: {
    gold: '/images/trophies/gold.png',
    silver: '/images/trophies/silver.png',
    bronze: '/images/trophies/bronze.png'
  }
};

// Placeholder image generator for development
export const generatePlaceholderImage = (width: number, height: number, text: string, color: string = '#1a1a1a') => {
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${color}"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle" dy=".3em">
        ${text}
      </text>
    </svg>
  `)}`;
};

// High-quality placeholder images with Australian car themes
export const placeholderImages = {
  rx3Main: generatePlaceholderImage(800, 600, 'Mazda RX-3 Savanna', '#8B0000'),
  commodoreMain: generatePlaceholderImage(800, 600, 'Holden Commodore VL', '#000080'),
  falconMain: generatePlaceholderImage(800, 600, 'Ford Falcon XB GT', '#006400'),
  toranaMain: generatePlaceholderImage(800, 600, 'Holden Torana XU-1', '#FF8C00'),
  
  burnoutEvent: generatePlaceholderImage(600, 400, 'Burnout Championship', '#FF4500'),
  driftEvent: generatePlaceholderImage(600, 400, 'Drift Series', '#4169E1'),
  raceEvent: generatePlaceholderImage(600, 400, 'Race Circuit', '#228B22'),
  showEvent: generatePlaceholderImage(600, 400, 'Car Show', '#9932CC'),
  
  heroBg: generatePlaceholderImage(1920, 1080, 'Australian Outback Racing', '#2F4F4F'),
  garageBg: generatePlaceholderImage(1920, 1080, 'Professional Garage', '#1C1C1C'),
  trackBg: generatePlaceholderImage(1920, 1080, 'Racing Circuit', '#2F2F2F')
};

export default {
  carImages,
  eventImages,
  backgroundImages,
  uiImages,
  placeholderImages,
  generatePlaceholderImage
};
