// High-quality car images and assets with real URLs
export const carImages = {
  // Mazda RX-3 Savanna
  rx3: {
    main: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    profile: 'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    front: 'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    rear: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    interior: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    engine: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    founders: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80&sat=2&hue=30',
    thumbnail: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
  },

  // Holden Commodore VL Turbo
  commodore: {
    main: 'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    profile: 'https://images.unsplash.com/photo-1583121274602-3e2820c69888?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    front: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    rear: 'https://images.unsplash.com/photo-1568605117036-5fe5e7bab0b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    interior: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    engine: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    thumbnail: 'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
  },

  // Ford Falcon XB GT
  falcon: {
    main: 'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    profile: 'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    front: 'https://images.unsplash.com/photo-1494905998402-395d579af36f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    rear: 'https://images.unsplash.com/photo-1583121274602-3e2820c69888?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    interior: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    engine: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    thumbnail: 'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
  },

  // Holden Torana XU-1
  torana: {
    main: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    profile: 'https://images.unsplash.com/photo-1568605117036-5fe5e7bab0b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    front: 'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    rear: 'https://images.unsplash.com/photo-1494905998402-395d579af36f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    interior: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    engine: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    thumbnail: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
  }
};

// Event and location images with real URLs
export const eventImages = {
  burnout: {
    championship: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    smoke: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    crowd: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
  },
  drift: {
    series: 'https://images.unsplash.com/photo-1568605117036-5fe5e7bab0b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    action: 'https://images.unsplash.com/photo-1583121274602-3e2820c69888?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    track: 'https://images.unsplash.com/photo-1494905998402-395d579af36f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
  },
  race: {
    circuit: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    start: 'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    finish: 'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
  },
  show: {
    display: 'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    judging: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
    awards: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
  }
};

// Background and environment images with real URLs
export const backgroundImages = {
  hero: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
  garage: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
  track: 'https://images.unsplash.com/photo-1494905998402-395d579af36f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
  workshop: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
  outback: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
  city: 'https://images.unsplash.com/photo-1514565131-fce0801e5785?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80'
};

// UI and icon images
export const uiImages = {
  logo: '/images/ui/logo.png',
  logoText: '/images/ui/logo-text.png',
  australianFlag: '/images/ui/australian-flag.png',
  foundersEdition: '/images/ui/founders-edition-badge.png',
  achievements: {
    welcome: '/images/achievements/welcome.png',
    firstBurnout: '/images/achievements/first-burnout.png',
    firstDrift: '/images/achievements/first-drift.png',
    speedDemon: '/images/achievements/speed-demon.png',
    styleMaster: '/images/achievements/style-master.png',
    perfectionist: '/images/achievements/perfectionist.png',
    legend: '/images/achievements/legend.png'
  },
  trophies: {
    gold: '/images/trophies/gold.png',
    silver: '/images/trophies/silver.png',
    bronze: '/images/trophies/bronze.png'
  }
};

// Placeholder image generator for development
export const generatePlaceholderImage = (width: number, height: number, text: string, color: string = '#1a1a1a') => {
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="${color}"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle" dy=".3em">
        ${text}
      </text>
    </svg>
  `)}`;
};

// High-quality images with real URLs as primary, enhanced placeholders as fallback
export const placeholderImages = {
  // Car images - using real URLs
  rx3Main: carImages.rx3.main,
  commodoreMain: carImages.commodore.main,
  falconMain: carImages.falcon.main,
  toranaMain: carImages.torana.main,

  // Event images - using real URLs
  burnoutEvent: eventImages.burnout.championship,
  driftEvent: eventImages.drift.series,
  raceEvent: eventImages.race.circuit,
  showEvent: eventImages.show.display,

  // Background images - using real URLs
  heroBg: backgroundImages.hero,
  garageBg: backgroundImages.garage,
  trackBg: backgroundImages.track,

  // Enhanced SVG placeholders for fallback
  rx3Placeholder: generatePlaceholderImage(800, 600, '🏎️ Mazda RX-3 Savanna', '#8B0000'),
  commodorePlaceholder: generatePlaceholderImage(800, 600, '🚗 Holden Commodore VL', '#000080'),
  falconPlaceholder: generatePlaceholderImage(800, 600, '🏁 Ford Falcon XB GT', '#006400'),
  toranaPlaceholder: generatePlaceholderImage(800, 600, '⚡ Holden Torana XU-1', '#FF8C00'),

  burnoutPlaceholder: generatePlaceholderImage(600, 400, '🔥 Burnout Championship', '#FF4500'),
  driftPlaceholder: generatePlaceholderImage(600, 400, '💨 Drift Series', '#4169E1'),
  racePlaceholder: generatePlaceholderImage(600, 400, '🏁 Race Circuit', '#228B22'),
  showPlaceholder: generatePlaceholderImage(600, 400, '✨ Car Show', '#9932CC')
};

export default {
  carImages,
  eventImages,
  backgroundImages,
  uiImages,
  placeholderImages,
  generatePlaceholderImage
};
