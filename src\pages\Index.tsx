
import React, { useState, useRef, useEffect, lazy, Suspense } from 'react';
import Navbar from '@/components/Navbar';
import Hero from '@/components/Hero';
// Lazy load heavy components
const CarShowcase = lazy(() => import('@/components/CarShowcase'));
const EventsPreview = lazy(() => import('@/components/EventsPreview'));
const FeatureSection = lazy(() => import('@/components/FeatureSection'));
const GameRoadmap = lazy(() => import('@/components/GameRoadmap'));
const CarCustomizer = lazy(() => import('@/components/CarCustomizer'));
import Footer from '@/components/Footer';
import Car3DViewer from '@/components/3D/Car3DViewer';
import AustralianEmblem from '@/components/AustralianEmblem';
import { Button } from '@/components/ui/button';
import { useToast } from "@/components/ui/use-toast";
import LazyLoad from '@/components/ui/lazy-load';
import { HeroBackground } from '@/components/graphics/EnhancedBackgrounds';

const Index = () => {
  const { toast } = useToast();
  const [isRevving, setIsRevving] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Car customization state
  const [carCustomization, setCarCustomization] = useState({
    bodyColor: '#FF0000',
    hasRacingStripes: false,
    hasAustralianFlag: true,
    wheelSize: 17,
    exhaustType: 'standard'
  });

  useEffect(() => {
    // Create audio element when component mounts
    audioRef.current = new Audio('/sounds/engine-rev.mp3');

    // Clean up when component unmounts
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  const handleRevEngine = () => {
    setIsRevving(true);

    // Play sound if enabled
    if (soundEnabled && audioRef.current) {
      audioRef.current.currentTime = 0; // Reset to start
      audioRef.current.play().catch(error => {
        console.error("Error playing sound:", error);
      });
    }

    toast({
      title: "Engine Revving!",
      description: "Feel the power of the iconic RX-3's rotary engine!",
      className: "bg-racing-black border-racing-yellow text-white",
    });

    setTimeout(() => setIsRevving(false), 3000);
  };

  return (
    <HeroBackground>
      <Navbar />
      <main>
        <Hero />

        {/* 3D Car Viewer */}
        <section className="py-12 bg-[#00008B] relative overflow-hidden">
          {/* Australian flag stars background (Southern Cross) */}
          <div className="absolute inset-0 overflow-hidden opacity-10">
            <div className="absolute top-[10%] left-[20%] w-6 h-6 bg-white rotate-45"></div>
            <div className="absolute top-[30%] left-[40%] w-4 h-4 bg-white rotate-45"></div>
            <div className="absolute top-[15%] left-[60%] w-8 h-8 bg-white rotate-45"></div>
            <div className="absolute top-[40%] left-[75%] w-5 h-5 bg-white rotate-45"></div>
            <div className="absolute top-[25%] left-[85%] w-7 h-7 bg-white rotate-45"></div>
          </div>

          <div className="container mx-auto">
            <div className="text-center mb-8 relative">
              <h2 className="text-3xl font-bold text-white">Interactive RX-3 Showcase</h2>
              <p className="text-gray-400 mt-2">Take a closer look at our 3D Mazda RX-3 model</p>
              <AustralianEmblem className="absolute top-0 right-0 opacity-70" size="md" />
            </div>
            <div className="rounded-lg overflow-hidden border-2 border-[#FF0000] shadow-lg shadow-[#FF0000]/20">
              <LazyLoad
                placeholder={
                  <div className="w-full h-[400px] bg-[#00008B] flex items-center justify-center">
                    <div className="text-white text-center">
                      <div className="animate-spin h-10 w-10 border-4 border-white rounded-full border-t-transparent mx-auto mb-4"></div>
                      <p>Loading 3D Model...</p>
                    </div>
                  </div>
                }
              >
                <Car3DViewer isRevving={isRevving} customization={carCustomization} />
              </LazyLoad>
            </div>
            <div className="mt-6 flex flex-col md:flex-row items-center justify-center gap-4">
              <div className="text-center text-sm text-gray-400 md:text-left">
                This 3D model represents our Founders Edition Mazda RX-3 with custom Australian styling.
                <br />More detailed models and customization options coming soon!
              </div>
              <div className="flex flex-col gap-2">
                <Button
                  className="bg-[#FF0000] hover:bg-red-700 text-white"
                  onClick={handleRevEngine}
                  disabled={isRevving}
                >
                  {isRevving ? "Revving..." : "Rev Engine"}
                </Button>
                <div className="flex items-center justify-center gap-2">
                  <input
                    type="checkbox"
                    id="sound-toggle"
                    checked={soundEnabled}
                    onChange={() => setSoundEnabled(!soundEnabled)}
                    className="w-4 h-4"
                  />
                  <label htmlFor="sound-toggle" className="text-xs text-gray-400">
                    Sound {soundEnabled ? "On" : "Off"}
                  </label>
                </div>
              </div>
            </div>

            {/* Car Customizer */}
            <div className="mt-12">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white">Customize Your RX-3</h3>
                <p className="text-gray-400 mt-2">Try our new customization options</p>
              </div>

              <Suspense fallback={<div className="py-10 text-center text-white">Loading customizer...</div>}>
                <LazyLoad>
                  <CarCustomizer
                    customization={carCustomization}
                    onCustomizationChange={setCarCustomization}
                  />
                </LazyLoad>
              </Suspense>
            </div>

            {/* Commonwealth Star (Bottom decoration) */}
            <div className="w-12 h-12 mx-auto mt-8 relative">
              <div className="absolute inset-0 bg-white rotate-45"></div>
              <div className="absolute inset-0 bg-white rotate-[22.5deg]"></div>
            </div>
          </div>
        </section>

        <Suspense fallback={<div className="py-20 text-center text-white">Loading content...</div>}>
          <LazyLoad>
            <CarShowcase />
          </LazyLoad>
        </Suspense>

        <Suspense fallback={<div className="py-20 text-center text-white">Loading events...</div>}>
          <LazyLoad>
            <EventsPreview />
          </LazyLoad>
        </Suspense>

        <Suspense fallback={<div className="py-20 text-center text-white">Loading roadmap...</div>}>
          <LazyLoad>
            <GameRoadmap />
          </LazyLoad>
        </Suspense>

        <Suspense fallback={<div className="py-20 text-center text-white">Loading features...</div>}>
          <LazyLoad>
            <FeatureSection />
          </LazyLoad>
        </Suspense>
      </main>
      <Footer />
    </HeroBackground>
  );
};

export default Index;
