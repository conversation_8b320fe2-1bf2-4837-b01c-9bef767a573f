
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const CarShowcase = () => {
  const [currentView, setCurrentView] = useState<'exterior' | 'interior' | 'engine'>('exterior');

  // Car gallery - using the uploaded images
  const carImages = {
    exterior: [
      "/lovable-uploads/20adddf2-9fe4-455f-8e10-8f4773d69892.png",
      "/lovable-uploads/d926514d-0e22-447a-a4f4-c174e60d75a1.png",
      "/lovable-uploads/9ae699d3-8205-4b18-8268-912dc2262eb8.png",
    ],
    interior: [
      "/lovable-uploads/a594cad8-350e-482b-b30c-73b539a00109.png",
    ],
    engine: [
      "/lovable-uploads/9e58dd2b-6e86-49da-bf47-452803c2f47c.png",
    ]
  };

  const [selectedImage, setSelectedImage] = useState<string>(carImages.exterior[0]);

  const carSpecs = {
    performance: {
      engine: "13B Rotary (Twin Rotor)",
      power: "250 HP",
      torque: "217 Nm",
      transmission: "5-Speed Manual",
      driveType: "Rear-Wheel Drive",
      acceleration: "5.8 sec (0-100 km/h)",
      topSpeed: "210 km/h"
    },
    dimensions: {
      length: "4,310 mm",
      width: "1,675 mm",
      height: "1,375 mm",
      weight: "1,050 kg",
      wheelbase: "2,310 mm",
      groundClearance: "140 mm"
    },
    features: [
      "Racing bucket seats",
      "Roll cage",
      "Racing harness",
      "Limited-slip differential",
      "Racing exhaust system",
      "Wider fenders",
      "Gold/Yellow 'Founders Edition' paint",
      "Dunlop racing tires",
      "Numbered plaque (1-100)"
    ]
  };

  return (
    <section className="py-16 bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-10">
          <Badge variant="outline" className="mb-4 bg-racing-red text-white border-racing-yellow px-4 py-1">
            EXCLUSIVE
          </Badge>
          <h2 className="text-4xl font-bold text-racing-yellow mb-4">Founders Edition</h2>
          <h3 className="text-3xl font-bold text-white mb-6">Mazda RX-3 Savanna Coupe</h3>
          <p className="text-gray-300 max-w-3xl mx-auto">
            This legendary Japanese import became an icon in Australian motorsport history. With its distinctive rotary engine sound and impressive power-to-weight ratio, the RX-3 dominated touring car racing in the early 1970s.
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          {/* Car viewer */}
          <div className="bg-black/40 rounded-lg p-4 border border-racing-yellow/20">
            <div className="relative aspect-video overflow-hidden rounded-md mb-4">
              <img 
                src={selectedImage} 
                alt="Mazda RX-3 Savanna" 
                className="w-full h-full object-cover"
              />
              <div className="absolute top-4 right-4">
                <Badge className="bg-gradient-to-r from-racing-yellow via-racing-red to-racing-yellow text-black font-bold">
                  Founders Edition
                </Badge>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-2">
              <Button 
                variant={currentView === 'exterior' ? "default" : "outline"}
                onClick={() => {
                  setCurrentView('exterior');
                  setSelectedImage(carImages.exterior[0]);
                }}
                className={currentView === 'exterior' ? "bg-racing-red text-white" : ""}
              >
                Exterior
              </Button>
              <Button 
                variant={currentView === 'interior' ? "default" : "outline"}
                onClick={() => {
                  setCurrentView('interior');
                  setSelectedImage(carImages.interior[0]);
                }}
                className={currentView === 'interior' ? "bg-racing-red text-white" : ""}
              >
                Interior
              </Button>
              <Button 
                variant={currentView === 'engine' ? "default" : "outline"}
                onClick={() => {
                  setCurrentView('engine');
                  setSelectedImage(carImages.engine[0]);
                }}
                className={currentView === 'engine' ? "bg-racing-red text-white" : ""}
              >
                Engine
              </Button>
            </div>
            
            <div className="mt-4 grid grid-cols-3 gap-2">
              {carImages[currentView].map((image, index) => (
                <div 
                  key={index}
                  className={`cursor-pointer rounded-md overflow-hidden border-2 ${selectedImage === image ? 'border-racing-yellow' : 'border-transparent'}`}
                  onClick={() => setSelectedImage(image)}
                >
                  <img src={image} alt={`Mazda RX-3 ${currentView} view ${index+1}`} className="w-full h-20 object-cover" />
                </div>
              ))}
            </div>
          </div>
          
          {/* Car specifications */}
          <div>
            <Tabs defaultValue="performance" className="w-full">
              <TabsList className="w-full grid grid-cols-3 mb-6">
                <TabsTrigger value="performance">Performance</TabsTrigger>
                <TabsTrigger value="dimensions">Dimensions</TabsTrigger>
                <TabsTrigger value="features">Features</TabsTrigger>
              </TabsList>
              
              <TabsContent value="performance">
                <Card className="bg-black/40 border-racing-yellow/20">
                  <CardContent className="pt-6">
                    <dl className="divide-y divide-gray-700">
                      {Object.entries(carSpecs.performance).map(([key, value]) => (
                        <div key={key} className="py-3 flex justify-between">
                          <dt className="text-gray-300 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</dt>
                          <dd className="text-racing-yellow font-medium">{value}</dd>
                        </div>
                      ))}
                    </dl>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="dimensions">
                <Card className="bg-black/40 border-racing-yellow/20">
                  <CardContent className="pt-6">
                    <dl className="divide-y divide-gray-700">
                      {Object.entries(carSpecs.dimensions).map(([key, value]) => (
                        <div key={key} className="py-3 flex justify-between">
                          <dt className="text-gray-300 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</dt>
                          <dd className="text-racing-yellow font-medium">{value}</dd>
                        </div>
                      ))}
                    </dl>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="features">
                <Card className="bg-black/40 border-racing-yellow/20">
                  <CardContent className="pt-6">
                    <ul className="space-y-3">
                      {carSpecs.features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-2">
                          <div className="bg-racing-red rounded-full w-2 h-2"></div>
                          <span className="text-gray-300">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
            
            <div className="mt-8 bg-racing-black p-4 rounded-lg border border-racing-yellow/30">
              <h4 className="text-racing-yellow font-bold mb-2">Founders Edition Exclusives:</h4>
              <p className="text-gray-300 mb-4">
                Only the first 100 registered players will receive this legendary car with special features, unique livery options, and a commemorative digital certificate of ownership.
              </p>
              <Button className="bg-racing-red hover:bg-red-700 text-white w-full">
                Register to Claim Yours
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CarShowcase;
