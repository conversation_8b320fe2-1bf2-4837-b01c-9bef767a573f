
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
                // Racing theme colors
                racing: {
                    yellow: '#FFD700',
                    red: '#FF0000',
                    green: '#008000',
                    black: '#000000',
                    checkered: '#F3F3F3',
                },
                // Australian flag colors
                australia: {
                    blue: '#00008B',     // Dark blue from Australian flag
                    red: '#FF0000',      // Red from Australian flag
                    white: '#FFFFFF',    // White from Australian flag
                    navy: '#000080',     // Navy blue variant
                    lightBlue: '#5B92E5' // Light blue for accents
                }
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: { height: '0' },
					to: { height: 'var(--radix-accordion-content-height)' }
				},
				'accordion-up': {
					from: { height: 'var(--radix-accordion-content-height)' },
					to: { height: '0' }
				},
                'burnout-smoke': {
                    '0%': { opacity: '0', transform: 'translateY(0) scale(0.8)' },
                    '20%': { opacity: '0.8' },
                    '100%': { opacity: '0', transform: 'translateY(-60px) scale(1.5)' }
                },
                'car-bounce': {
                    '0%, 100%': { transform: 'translateY(0)' },
                    '50%': { transform: 'translateY(-10px)' }
                },
                'rev-engine': {
                    '0%, 100%': { transform: 'rotate(0deg)' },
                    '25%': { transform: 'rotate(0.5deg)' },
                    '75%': { transform: 'rotate(-0.5deg)' }
                },
                'star-twinkle': {
                    '0%, 100%': { opacity: '1' },
                    '50%': { opacity: '0.5' }
                }
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
                'burnout-smoke': 'burnout-smoke 3s ease-out infinite',
                'car-bounce': 'car-bounce 0.5s ease-in-out',
                'rev-engine': 'rev-engine 0.1s ease-in-out infinite',
                'star-twinkle': 'star-twinkle 2s ease-in-out infinite'
			},
            backgroundImage: {
                'checkered-flag': 'repeating-conic-gradient(#000 0%, #000 25%, #fff 0%, #fff 50%)',
                'tire-marks': 'url("/tire-tracks.svg")',
                'aus-flag-gradient': 'linear-gradient(to right, #00008B, #FF0000, #FFFFFF)',
            },
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
