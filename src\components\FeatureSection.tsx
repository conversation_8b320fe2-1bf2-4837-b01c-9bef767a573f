
import React from 'react';

const features = [
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <circle cx="12" cy="12" r="10"/>
        <path d="M16.2 7.8l-2 6.3-6.4 2.1 2-6.3z"/>
      </svg>
    ),
    title: "Authentic Racing",
    description: "Experience realistic car physics, handling, and damage modeling that captures the true essence of Australian motorsport."
  },
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M19 17h2v-5h-9V6H3v11h2"/>
        <circle cx="7" cy="17" r="2"/>
        <circle cx="17" cy="17" r="2"/>
      </svg>
    ),
    title: "Iconic Cars",
    description: "Drive legendary Australian and Japanese cars including Holden, Ford, and the exclusive Mazda RX-3 Savanna Coupe Founders Edition."
  },
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"/>
      </svg>
    ),
    title: "Customization",
    description: "Modify your car with engine swaps, turbos, exhausts, and visual customizations to create your dream ride."
  },
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
      </svg>
    ),
    title: "Community Events",
    description: "Join burnout competitions, drag races, car shows, and more in a thriving community of Aussie car enthusiasts."
  },
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <circle cx="12" cy="8" r="7"/>
        <polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"/>
      </svg>
    ),
    title: "Achievements",
    description: "Earn badges, trophies and climb the leaderboards to establish yourself as a true Aussie Burnout Legend."
  },
  {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <rect x="5" y="2" width="14" height="20" rx="2" ry="2"/>
        <line x1="12" y1="18" x2="12.01" y2="18"/>
      </svg>
    ),
    title: "Mobile Integration",
    description: "Scan QR codes to import custom liveries, receive event notifications, and manage your garage on the go."
  }
];

const FeatureSection = () => {
  return (
    <section className="py-16 bg-gradient-to-b from-black to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-racing-yellow mb-4">Game Features</h2>
          <p className="text-gray-300 max-w-3xl mx-auto">
            Aussie Burnout Legends offers the most authentic and exciting Australian car culture experience ever created.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="bg-black/40 p-6 rounded-lg border border-racing-yellow/20 hover:border-racing-yellow/60 transition-colors duration-300"
            >
              <div className="h-12 w-12 bg-racing-red rounded-lg p-2 text-white mb-4 flex items-center justify-center">
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold text-racing-yellow mb-2">{feature.title}</h3>
              <p className="text-gray-300">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
      
      {/* Decorative tire tracks */}
      <div className="mt-16 h-16 opacity-10 tire-track"></div>
    </section>
  );
};

export default FeatureSection;
