import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Progress } from "@/components/ui/progress";
import AustralianEmblem from '@/components/AustralianEmblem';

interface EnhancedLoadingScreenProps {
  isLoading: boolean;
  progress?: number;
  message?: string;
  variant?: 'default' | 'game' | 'garage' | 'minimal';
  onComplete?: () => void;
}

const EnhancedLoadingScreen: React.FC<EnhancedLoadingScreenProps> = ({
  isLoading,
  progress = 0,
  message = "Loading...",
  variant = 'default',
  onComplete
}) => {
  const [displayProgress, setDisplayProgress] = useState(0);
  const [currentMessage, setCurrentMessage] = useState(message);

  const loadingMessages = [
    "Starting engines...",
    "Warming up tires...",
    "Checking oil levels...",
    "Tuning exhaust...",
    "Loading Australian legends...",
    "Preparing burnout pad...",
    "Almost ready to rev..."
  ];

  useEffect(() => {
    if (variant === 'game') {
      const messageInterval = setInterval(() => {
        setCurrentMessage(loadingMessages[Math.floor(Math.random() * loadingMessages.length)]);
      }, 1500);

      return () => clearInterval(messageInterval);
    }
  }, [variant]);

  useEffect(() => {
    const progressInterval = setInterval(() => {
      setDisplayProgress(prev => {
        const target = progress || (isLoading ? 100 : 0);
        const diff = target - prev;
        const increment = Math.max(1, Math.abs(diff) * 0.1);
        
        if (Math.abs(diff) < 1) {
          if (target >= 100 && onComplete) {
            setTimeout(onComplete, 500);
          }
          return target;
        }
        
        return prev + (diff > 0 ? increment : -increment);
      });
    }, 50);

    return () => clearInterval(progressInterval);
  }, [progress, isLoading, onComplete]);

  if (!isLoading && displayProgress <= 0) return null;

  const getVariantStyles = () => {
    switch (variant) {
      case 'game':
        return {
          background: 'linear-gradient(135deg, rgba(47, 79, 79, 0.95) 0%, rgba(25, 25, 112, 0.9) 50%, rgba(0, 0, 0, 0.98) 100%)',
          accent: 'text-green-400',
          progressColor: 'from-green-400 to-green-600'
        };
      case 'garage':
        return {
          background: 'linear-gradient(180deg, rgba(28, 28, 28, 0.98) 0%, rgba(10, 10, 10, 0.99) 100%)',
          accent: 'text-racing-yellow',
          progressColor: 'from-racing-yellow to-orange-500'
        };
      case 'minimal':
        return {
          background: 'rgba(0, 0, 0, 0.8)',
          accent: 'text-white',
          progressColor: 'from-white to-gray-300'
        };
      default:
        return {
          background: 'linear-gradient(135deg, rgba(0, 4, 36, 0.98) 0%, rgba(0, 20, 64, 0.95) 50%, rgba(139, 0, 0, 0.9) 100%)',
          accent: 'text-racing-yellow',
          progressColor: 'from-racing-yellow to-racing-red'
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <AnimatePresence>
      {(isLoading || displayProgress > 0) && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center"
          style={{ background: styles.background }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Background Effects */}
          <div className="absolute inset-0 overflow-hidden">
            {/* Animated particles */}
            {Array.from({ length: 20 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-racing-yellow/30 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, -20, 0],
                  opacity: [0.3, 0.8, 0.3],
                  scale: [1, 1.5, 1]
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  delay: Math.random() * 2
                }}
              />
            ))}

            {/* Racing stripes effect */}
            <motion.div
              className="absolute inset-0 opacity-10"
              style={{
                background: `repeating-linear-gradient(
                  45deg,
                  transparent,
                  transparent 10px,
                  rgba(255, 215, 0, 0.1) 10px,
                  rgba(255, 215, 0, 0.1) 20px
                )`
              }}
              animate={{
                backgroundPosition: ['0px 0px', '40px 40px']
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </div>

          {/* Main Content */}
          <div className="relative z-10 text-center max-w-md mx-auto px-6">
            {/* Logo/Emblem */}
            <motion.div
              className="mb-8"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ 
                type: "spring", 
                stiffness: 200, 
                damping: 15,
                delay: 0.2 
              }}
            >
              {variant === 'minimal' ? (
                <div className="w-16 h-16 mx-auto bg-white rounded-full flex items-center justify-center">
                  <div className="w-8 h-8 border-4 border-gray-800 border-t-transparent rounded-full animate-spin" />
                </div>
              ) : (
                <AustralianEmblem size="lg" className="mx-auto" />
              )}
            </motion.div>

            {/* Title */}
            {variant !== 'minimal' && (
              <motion.h1
                className={`text-3xl md:text-4xl font-bold mb-2 ${styles.accent}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                Aussie Burnout Legends
              </motion.h1>
            )}

            {/* Loading Message */}
            <motion.p
              className="text-gray-300 mb-8 text-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
              key={currentMessage}
            >
              {currentMessage}
            </motion.p>

            {/* Progress Bar */}
            <motion.div
              className="mb-6"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.8 }}
            >
              <div className="relative">
                <Progress 
                  value={displayProgress} 
                  className="h-3 bg-gray-800/50 border border-gray-600"
                />
                <motion.div
                  className={`absolute inset-0 h-3 bg-gradient-to-r ${styles.progressColor} rounded-full`}
                  style={{ width: `${displayProgress}%` }}
                  initial={{ width: 0 }}
                  animate={{ width: `${displayProgress}%` }}
                  transition={{ duration: 0.3 }}
                />
                
                {/* Progress glow effect */}
                <motion.div
                  className={`absolute inset-0 h-3 bg-gradient-to-r ${styles.progressColor} rounded-full opacity-50 blur-sm`}
                  style={{ width: `${displayProgress}%` }}
                  animate={{
                    opacity: [0.3, 0.7, 0.3]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </div>
              
              <div className="flex justify-between text-sm text-gray-400 mt-2">
                <span>0%</span>
                <span className={styles.accent}>{Math.round(displayProgress)}%</span>
                <span>100%</span>
              </div>
            </motion.div>

            {/* Loading Animation */}
            {variant === 'game' && (
              <motion.div
                className="flex justify-center space-x-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1 }}
              >
                {Array.from({ length: 3 }).map((_, i) => (
                  <motion.div
                    key={i}
                    className="w-3 h-3 bg-racing-yellow rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.5, 1, 0.5]
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      delay: i * 0.2
                    }}
                  />
                ))}
              </motion.div>
            )}

            {/* Engine rev effect for game variant */}
            {variant === 'game' && displayProgress > 80 && (
              <motion.div
                className="mt-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <motion.div
                  className="text-2xl"
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{
                    duration: 0.5,
                    repeat: Infinity
                  }}
                >
                  🏎️
                </motion.div>
                <p className="text-green-400 text-sm mt-2 font-semibold">
                  Ready to burn rubber!
                </p>
              </motion.div>
            )}

            {/* Tips for longer loading */}
            {displayProgress > 50 && variant !== 'minimal' && (
              <motion.div
                className="mt-8 p-4 bg-black/30 rounded-lg border border-gray-700"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.5 }}
              >
                <p className="text-gray-400 text-sm">
                  <strong className={styles.accent}>Pro Tip:</strong> Use WASD or arrow keys to control your car, 
                  and hold Space for handbrake to perform epic burnouts!
                </p>
              </motion.div>
            )}
          </div>

          {/* Corner decorations */}
          {variant !== 'minimal' && (
            <>
              <motion.div
                className="absolute top-4 left-4 w-16 h-16 border-l-4 border-t-4 border-racing-yellow/30"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.2 }}
              />
              <motion.div
                className="absolute top-4 right-4 w-16 h-16 border-r-4 border-t-4 border-racing-yellow/30"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.3 }}
              />
              <motion.div
                className="absolute bottom-4 left-4 w-16 h-16 border-l-4 border-b-4 border-racing-yellow/30"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.4 }}
              />
              <motion.div
                className="absolute bottom-4 right-4 w-16 h-16 border-r-4 border-b-4 border-racing-yellow/30"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.5 }}
              />
            </>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EnhancedLoadingScreen;
