import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, Clock, MapPin, Users, Trophy, Star, Crown } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import AustralianEmblem from '@/components/AustralianEmblem';

interface Event {
  id: string;
  title: string;
  description: string;
  type: 'burnout' | 'drift' | 'race' | 'show';
  date: string;
  time: string;
  location: string;
  maxParticipants: number;
  currentParticipants: number;
  entryFee: number;
  prizes: {
    first: string;
    second: string;
    third: string;
  };
  requirements: {
    minLevel?: number;
    carType?: string;
    foundersOnly?: boolean;
  };
  status: 'upcoming' | 'registration' | 'ongoing' | 'completed';
  registered: boolean;
}

const EVENTS: Event[] = [
  {
    id: 'aussie-burnout-championship',
    title: 'Aussie Burnout Championship',
    description: 'The ultimate burnout competition featuring the best drivers from across Australia. Show off your tire-smoking skills!',
    type: 'burnout',
    date: '2024-02-15',
    time: '19:00 AEDT',
    location: 'Sydney Motorsport Park',
    maxParticipants: 50,
    currentParticipants: 32,
    entryFee: 0,
    prizes: {
      first: 'Holden Commodore VL Turbo + $1000',
      second: 'Custom Exhaust System',
      third: 'Racing Stripes Package'
    },
    requirements: {
      minLevel: 5
    },
    status: 'registration',
    registered: false
  },
  {
    id: 'founders-drift-series',
    title: 'Founders Edition Drift Series',
    description: 'Exclusive drift competition for Founders Edition members. Compete for legendary prizes!',
    type: 'drift',
    date: '2024-02-20',
    time: '20:00 AEDT',
    location: 'Melbourne Drift Circuit',
    maxParticipants: 25,
    currentParticipants: 18,
    entryFee: 0,
    prizes: {
      first: 'Ford Falcon XB GT + Founders Badge',
      second: 'Premium Wheel Package',
      third: 'Exclusive Livery Design'
    },
    requirements: {
      foundersOnly: true
    },
    status: 'registration',
    registered: false
  },
  {
    id: 'weekend-warriors',
    title: 'Weekend Warriors Race',
    description: 'Casual racing event for all skill levels. Perfect for newcomers to test their skills!',
    type: 'race',
    date: '2024-02-17',
    time: '15:00 AEDT',
    location: 'Brisbane Street Circuit',
    maxParticipants: 100,
    currentParticipants: 67,
    entryFee: 0,
    prizes: {
      first: 'Torana XU-1 + Racing Suit',
      second: 'Performance Upgrade Package',
      third: 'Custom Paint Job'
    },
    requirements: {},
    status: 'registration',
    registered: false
  },
  {
    id: 'car-show-spectacular',
    title: 'Australian Car Show Spectacular',
    description: 'Show off your customized rides in this beauty competition. Judged by community votes!',
    type: 'show',
    date: '2024-02-25',
    time: '14:00 AEDT',
    location: 'Adelaide Convention Centre',
    maxParticipants: 200,
    currentParticipants: 89,
    entryFee: 0,
    prizes: {
      first: 'Ultimate Customization Package',
      second: 'Premium Paint Collection',
      third: 'Wheel & Tire Package'
    },
    requirements: {},
    status: 'registration',
    registered: false
  }
];

interface EventRegistrationProps {
  className?: string;
}

const EventRegistration: React.FC<EventRegistrationProps> = ({ className }) => {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [events, setEvents] = useState(EVENTS);
  const [selectedTab, setSelectedTab] = useState('all');

  const handleRegister = (eventId: string) => {
    if (!isAuthenticated) {
      toast({
        title: "Login Required",
        description: "Please log in to register for events.",
        variant: "destructive",
      });
      return;
    }

    const event = events.find(e => e.id === eventId);
    if (!event) return;

    // Check requirements
    if (event.requirements.foundersOnly && !user?.foundersEdition) {
      toast({
        title: "Founders Edition Required",
        description: "This event is exclusive to Founders Edition members.",
        variant: "destructive",
      });
      return;
    }

    if (event.currentParticipants >= event.maxParticipants) {
      toast({
        title: "Event Full",
        description: "This event has reached maximum capacity.",
        variant: "destructive",
      });
      return;
    }

    // Register for event
    setEvents(prevEvents => 
      prevEvents.map(e => 
        e.id === eventId 
          ? { ...e, registered: true, currentParticipants: e.currentParticipants + 1 }
          : e
      )
    );

    toast({
      title: "Registration Successful!",
      description: `You've been registered for ${event.title}`,
      className: "bg-racing-black border-racing-yellow text-white",
    });
  };

  const handleUnregister = (eventId: string) => {
    const event = events.find(e => e.id === eventId);
    if (!event) return;

    setEvents(prevEvents => 
      prevEvents.map(e => 
        e.id === eventId 
          ? { ...e, registered: false, currentParticipants: e.currentParticipants - 1 }
          : e
      )
    );

    toast({
      title: "Unregistered",
      description: `You've been removed from ${event.title}`,
      className: "bg-racing-black border-racing-yellow text-white",
    });
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'burnout': return '🔥';
      case 'drift': return '💨';
      case 'race': return '🏁';
      case 'show': return '✨';
      default: return '🏆';
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'burnout': return 'bg-red-500';
      case 'drift': return 'bg-blue-500';
      case 'race': return 'bg-green-500';
      case 'show': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const filteredEvents = selectedTab === 'all' 
    ? events 
    : selectedTab === 'registered'
    ? events.filter(e => e.registered)
    : events.filter(e => e.type === selectedTab);

  return (
    <div className={`${className}`}>
      <Card className="bg-black/40 border-racing-yellow/20">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl text-racing-yellow">Event Registration</CardTitle>
              <CardDescription className="text-gray-400">
                Join exciting events and compete with other players
              </CardDescription>
            </div>
            <AustralianEmblem size="sm" className="opacity-70" />
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="grid grid-cols-6 mb-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="burnout">Burnout</TabsTrigger>
              <TabsTrigger value="drift">Drift</TabsTrigger>
              <TabsTrigger value="race">Race</TabsTrigger>
              <TabsTrigger value="show">Show</TabsTrigger>
              <TabsTrigger value="registered">My Events</TabsTrigger>
            </TabsList>
            
            <div className="space-y-4">
              {filteredEvents.map((event) => (
                <Card 
                  key={event.id} 
                  className={`${event.registered 
                    ? 'bg-racing-yellow/10 border-racing-yellow/40' 
                    : 'bg-gray-900/40 border-gray-700'
                  }`}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start gap-4">
                        <div className="text-3xl">{getEventTypeIcon(event.type)}</div>
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="text-lg font-semibold text-white">{event.title}</h3>
                            <Badge className={`${getEventTypeColor(event.type)} text-white text-xs`}>
                              {event.type}
                            </Badge>
                            {event.requirements.foundersOnly && (
                              <Crown className="w-4 h-4 text-racing-yellow" />
                            )}
                          </div>
                          <p className="text-sm text-gray-400 mb-3">{event.description}</p>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div className="flex items-center gap-1 text-gray-300">
                              <Calendar className="w-4 h-4" />
                              <span>{new Date(event.date).toLocaleDateString()}</span>
                            </div>
                            <div className="flex items-center gap-1 text-gray-300">
                              <Clock className="w-4 h-4" />
                              <span>{event.time}</span>
                            </div>
                            <div className="flex items-center gap-1 text-gray-300">
                              <MapPin className="w-4 h-4" />
                              <span>{event.location}</span>
                            </div>
                            <div className="flex items-center gap-1 text-gray-300">
                              <Users className="w-4 h-4" />
                              <span>{event.currentParticipants}/{event.maxParticipants}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        {event.registered ? (
                          <Button 
                            variant="outline" 
                            onClick={() => handleUnregister(event.id)}
                            className="mb-2"
                          >
                            Unregister
                          </Button>
                        ) : (
                          <Button 
                            className="bg-racing-red hover:bg-red-700 text-white mb-2"
                            onClick={() => handleRegister(event.id)}
                            disabled={event.currentParticipants >= event.maxParticipants}
                          >
                            {event.currentParticipants >= event.maxParticipants ? 'Full' : 'Register'}
                          </Button>
                        )}
                        
                        <div className="text-xs text-gray-500">
                          {event.entryFee > 0 ? `$${event.entryFee}` : 'Free'}
                        </div>
                      </div>
                    </div>
                    
                    <div className="border-t border-gray-700 pt-4">
                      <div className="flex items-center gap-1 mb-2">
                        <Trophy className="w-4 h-4 text-racing-yellow" />
                        <span className="text-sm font-medium text-white">Prizes</span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs">
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 text-yellow-500" />
                          <span className="text-gray-400">1st:</span>
                          <span className="text-white">{event.prizes.first}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 text-gray-400" />
                          <span className="text-gray-400">2nd:</span>
                          <span className="text-white">{event.prizes.second}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="w-3 h-3 text-orange-500" />
                          <span className="text-gray-400">3rd:</span>
                          <span className="text-white">{event.prizes.third}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {filteredEvents.length === 0 && (
                <div className="text-center py-12">
                  <Trophy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl text-white mb-2">No Events Found</h3>
                  <p className="text-gray-400">
                    {selectedTab === 'registered' 
                      ? "You haven't registered for any events yet." 
                      : "No events available in this category."}
                  </p>
                </div>
              )}
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default EventRegistration;
