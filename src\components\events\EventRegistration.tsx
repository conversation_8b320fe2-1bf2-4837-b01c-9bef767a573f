import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, Clock, MapPin, Users, Trophy, Star, Crown } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import AustralianEmblem from '@/components/AustralianEmblem';
import EnhancedEventCard from '@/components/graphics/EnhancedEventCard';
import { motion } from 'framer-motion';

interface Event {
  id: string;
  title: string;
  description: string;
  type: 'burnout' | 'drift' | 'race' | 'show';
  date: string;
  time: string;
  location: string;
  maxParticipants: number;
  currentParticipants: number;
  entryFee: number;
  prizes: {
    first: string;
    second: string;
    third: string;
  };
  requirements: {
    minLevel?: number;
    carType?: string;
    foundersOnly?: boolean;
  };
  status: 'upcoming' | 'registration' | 'ongoing' | 'completed';
  registered: boolean;
}

const EVENTS: Event[] = [
  {
    id: 'aussie-burnout-championship',
    title: 'Aussie Burnout Championship',
    description: 'The ultimate burnout competition featuring the best drivers from across Australia. Show off your tire-smoking skills!',
    type: 'burnout',
    date: '2024-02-15',
    time: '19:00 AEDT',
    location: 'Sydney Motorsport Park',
    maxParticipants: 50,
    currentParticipants: 32,
    entryFee: 0,
    prizes: {
      first: 'Holden Commodore VL Turbo + $1000',
      second: 'Custom Exhaust System',
      third: 'Racing Stripes Package'
    },
    requirements: {
      minLevel: 5
    },
    status: 'registration',
    registered: false
  },
  {
    id: 'founders-drift-series',
    title: 'Founders Edition Drift Series',
    description: 'Exclusive drift competition for Founders Edition members. Compete for legendary prizes!',
    type: 'drift',
    date: '2024-02-20',
    time: '20:00 AEDT',
    location: 'Melbourne Drift Circuit',
    maxParticipants: 25,
    currentParticipants: 18,
    entryFee: 0,
    prizes: {
      first: 'Ford Falcon XB GT + Founders Badge',
      second: 'Premium Wheel Package',
      third: 'Exclusive Livery Design'
    },
    requirements: {
      foundersOnly: true
    },
    status: 'registration',
    registered: false
  },
  {
    id: 'weekend-warriors',
    title: 'Weekend Warriors Race',
    description: 'Casual racing event for all skill levels. Perfect for newcomers to test their skills!',
    type: 'race',
    date: '2024-02-17',
    time: '15:00 AEDT',
    location: 'Brisbane Street Circuit',
    maxParticipants: 100,
    currentParticipants: 67,
    entryFee: 0,
    prizes: {
      first: 'Torana XU-1 + Racing Suit',
      second: 'Performance Upgrade Package',
      third: 'Custom Paint Job'
    },
    requirements: {},
    status: 'registration',
    registered: false
  },
  {
    id: 'car-show-spectacular',
    title: 'Australian Car Show Spectacular',
    description: 'Show off your customized rides in this beauty competition. Judged by community votes!',
    type: 'show',
    date: '2024-02-25',
    time: '14:00 AEDT',
    location: 'Adelaide Convention Centre',
    maxParticipants: 200,
    currentParticipants: 89,
    entryFee: 0,
    prizes: {
      first: 'Ultimate Customization Package',
      second: 'Premium Paint Collection',
      third: 'Wheel & Tire Package'
    },
    requirements: {},
    status: 'registration',
    registered: false
  }
];

interface EventRegistrationProps {
  className?: string;
}

const EventRegistration: React.FC<EventRegistrationProps> = ({ className }) => {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [events, setEvents] = useState(EVENTS);
  const [selectedTab, setSelectedTab] = useState('all');

  const handleRegister = (eventId: string) => {
    if (!isAuthenticated) {
      toast({
        title: "Login Required",
        description: "Please log in to register for events.",
        variant: "destructive",
      });
      return;
    }

    const event = events.find(e => e.id === eventId);
    if (!event) return;

    // Check requirements
    if (event.requirements.foundersOnly && !user?.foundersEdition) {
      toast({
        title: "Founders Edition Required",
        description: "This event is exclusive to Founders Edition members.",
        variant: "destructive",
      });
      return;
    }

    if (event.currentParticipants >= event.maxParticipants) {
      toast({
        title: "Event Full",
        description: "This event has reached maximum capacity.",
        variant: "destructive",
      });
      return;
    }

    // Register for event
    setEvents(prevEvents =>
      prevEvents.map(e =>
        e.id === eventId
          ? { ...e, registered: true, currentParticipants: e.currentParticipants + 1 }
          : e
      )
    );

    toast({
      title: "Registration Successful!",
      description: `You've been registered for ${event.title}`,
      className: "bg-racing-black border-racing-yellow text-white",
    });
  };

  const handleUnregister = (eventId: string) => {
    const event = events.find(e => e.id === eventId);
    if (!event) return;

    setEvents(prevEvents =>
      prevEvents.map(e =>
        e.id === eventId
          ? { ...e, registered: false, currentParticipants: e.currentParticipants - 1 }
          : e
      )
    );

    toast({
      title: "Unregistered",
      description: `You've been removed from ${event.title}`,
      className: "bg-racing-black border-racing-yellow text-white",
    });
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'burnout': return '🔥';
      case 'drift': return '💨';
      case 'race': return '🏁';
      case 'show': return '✨';
      default: return '🏆';
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'burnout': return 'bg-red-500';
      case 'drift': return 'bg-blue-500';
      case 'race': return 'bg-green-500';
      case 'show': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const filteredEvents = selectedTab === 'all'
    ? events
    : selectedTab === 'registered'
    ? events.filter(e => e.registered)
    : events.filter(e => e.type === selectedTab);

  return (
    <div className={`${className}`}>
      <Card className="bg-black/40 border-racing-yellow/20">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl text-racing-yellow">Event Registration</CardTitle>
              <CardDescription className="text-gray-400">
                Join exciting events and compete with other players
              </CardDescription>
            </div>
            <AustralianEmblem size="sm" className="opacity-70" />
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="grid grid-cols-6 mb-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="burnout">Burnout</TabsTrigger>
              <TabsTrigger value="drift">Drift</TabsTrigger>
              <TabsTrigger value="race">Race</TabsTrigger>
              <TabsTrigger value="show">Show</TabsTrigger>
              <TabsTrigger value="registered">My Events</TabsTrigger>
            </TabsList>

            <motion.div
              className="grid grid-cols-1 lg:grid-cols-2 gap-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {filteredEvents.map((event, index) => (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <EnhancedEventCard
                    {...event}
                    onRegister={() => handleRegister(event.id)}
                    onUnregister={() => handleUnregister(event.id)}
                    onViewDetails={() => {/* Handle view details */}}
                  />
                </motion.div>
              ))}

              {filteredEvents.length === 0 && (
                <motion.div
                  className="col-span-full text-center py-12"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <Trophy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl text-white mb-2">No Events Found</h3>
                  <p className="text-gray-400">
                    {selectedTab === 'registered'
                      ? "You haven't registered for any events yet."
                      : "No events available in this category."}
                  </p>
                </motion.div>
              )}
            </motion.div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default EventRegistration;
