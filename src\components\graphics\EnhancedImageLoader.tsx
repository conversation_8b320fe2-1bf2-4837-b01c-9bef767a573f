import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { placeholderImages } from '@/assets/images/cars';

interface EnhancedImageLoaderProps {
  src: string;
  alt: string;
  fallbackSrc?: string;
  placeholder?: string;
  className?: string;
  width?: number;
  height?: number;
  quality?: 'low' | 'medium' | 'high' | 'ultra';
  lazy?: boolean;
  blur?: boolean;
  overlay?: boolean;
  overlayColor?: string;
  onLoad?: () => void;
  onError?: () => void;
  children?: React.ReactNode;
}

const EnhancedImageLoader: React.FC<EnhancedImageLoaderProps> = ({
  src,
  alt,
  fallbackSrc,
  placeholder,
  className = '',
  width,
  height,
  quality = 'high',
  lazy = true,
  blur = false,
  overlay = false,
  overlayColor = 'rgba(0, 0, 0, 0.3)',
  onLoad,
  onError,
  children
}) => {
  const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading');
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [isInView, setIsInView] = useState(!lazy);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isInView]);

  // Image loading logic
  useEffect(() => {
    if (!isInView) return;

    const img = new Image();
    
    const handleLoad = () => {
      setCurrentSrc(getOptimizedSrc(src, quality, width, height));
      setImageState('loaded');
      onLoad?.();
    };

    const handleError = () => {
      if (fallbackSrc && currentSrc !== fallbackSrc) {
        // Try fallback image
        const fallbackImg = new Image();
        fallbackImg.onload = () => {
          setCurrentSrc(fallbackSrc);
          setImageState('loaded');
        };
        fallbackImg.onerror = () => {
          setCurrentSrc(placeholder || generateFallbackImage(alt, width, height));
          setImageState('error');
          onError?.();
        };
        fallbackImg.src = fallbackSrc;
      } else {
        setCurrentSrc(placeholder || generateFallbackImage(alt, width, height));
        setImageState('error');
        onError?.();
      }
    };

    img.onload = handleLoad;
    img.onerror = handleError;
    img.src = getOptimizedSrc(src, quality, width, height);

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [src, fallbackSrc, placeholder, quality, width, height, isInView, alt, onLoad, onError, currentSrc]);

  const getOptimizedSrc = (originalSrc: string, quality: string, w?: number, h?: number) => {
    // If it's an Unsplash URL, add optimization parameters
    if (originalSrc.includes('unsplash.com')) {
      const url = new URL(originalSrc);
      
      // Set quality
      const qualityMap = {
        low: '60',
        medium: '75',
        high: '85',
        ultra: '95'
      };
      url.searchParams.set('q', qualityMap[quality]);
      
      // Set dimensions if provided
      if (w) url.searchParams.set('w', w.toString());
      if (h) url.searchParams.set('h', h.toString());
      
      // Add format optimization
      url.searchParams.set('fm', 'webp');
      url.searchParams.set('auto', 'format');
      
      return url.toString();
    }
    
    return originalSrc;
  };

  const generateFallbackImage = (altText: string, w?: number, h?: number) => {
    const width = w || 800;
    const height = h || 600;
    const color = '#1a1a1a';
    const textColor = '#ffffff';
    
    return `data:image/svg+xml,${encodeURIComponent(`
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="${textColor}" stroke-width="0.5" opacity="0.1"/>
          </pattern>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:#2a2a2a;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)"/>
        <rect width="100%" height="100%" fill="url(#grid)"/>
        <circle cx="${width/2}" cy="${height/2 - 30}" r="30" fill="${textColor}" opacity="0.3"/>
        <text x="${width/2}" y="${height/2 - 30}" text-anchor="middle" dy="0.3em" font-family="Arial, sans-serif" font-size="24" fill="${textColor}" opacity="0.6">🏎️</text>
        <text x="${width/2}" y="${height/2 + 20}" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="${textColor}" opacity="0.8">${altText}</text>
        <text x="${width/2}" y="${height/2 + 45}" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="${textColor}" opacity="0.5">Image not available</text>
      </svg>
    `)}`;
  };

  const getBlurDataURL = () => {
    return `data:image/svg+xml,${encodeURIComponent(`
      <svg width="40" height="30" xmlns="http://www.w3.org/2000/svg">
        <filter id="blur">
          <feGaussianBlur stdDeviation="2"/>
        </filter>
        <rect width="100%" height="100%" fill="#1a1a1a" filter="url(#blur)"/>
      </svg>
    `)}`;
  };

  return (
    <div 
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={{ width, height }}
    >
      <AnimatePresence mode="wait">
        {/* Loading State */}
        {imageState === 'loading' && (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center bg-gray-900"
          >
            <div className="text-center">
              <motion.div
                className="w-12 h-12 border-4 border-racing-yellow border-t-transparent rounded-full mx-auto mb-4"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
              <p className="text-gray-400 text-sm">Loading image...</p>
            </div>
          </motion.div>
        )}

        {/* Loaded State */}
        {imageState === 'loaded' && currentSrc && (
          <motion.div
            key="loaded"
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="relative w-full h-full"
          >
            {/* Blur placeholder */}
            {blur && (
              <img
                src={getBlurDataURL()}
                alt=""
                className="absolute inset-0 w-full h-full object-cover filter blur-sm scale-110"
              />
            )}
            
            {/* Main image */}
            <img
              ref={imgRef}
              src={currentSrc}
              alt={alt}
              className="w-full h-full object-cover transition-all duration-500"
              loading={lazy ? "lazy" : "eager"}
              decoding="async"
            />
            
            {/* Overlay */}
            {overlay && (
              <div 
                className="absolute inset-0"
                style={{ backgroundColor: overlayColor }}
              />
            )}
            
            {/* Content overlay */}
            {children && (
              <div className="absolute inset-0 flex items-center justify-center">
                {children}
              </div>
            )}
          </motion.div>
        )}

        {/* Error State */}
        {imageState === 'error' && (
          <motion.div
            key="error"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="relative w-full h-full"
          >
            <img
              src={currentSrc}
              alt={alt}
              className="w-full h-full object-cover"
            />
            {children && (
              <div className="absolute inset-0 flex items-center justify-center">
                {children}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Progressive enhancement indicators */}
      {imageState === 'loaded' && quality === 'ultra' && (
        <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded opacity-75">
          HD
        </div>
      )}
    </div>
  );
};

// Specialized image components for different use cases
export const CarImage: React.FC<Omit<EnhancedImageLoaderProps, 'fallbackSrc'> & { carModel?: string }> = ({ 
  carModel, 
  ...props 
}) => {
  const fallbackSrc = carModel ? placeholderImages[`${carModel}Placeholder` as keyof typeof placeholderImages] : undefined;
  
  return (
    <EnhancedImageLoader
      {...props}
      fallbackSrc={fallbackSrc}
      quality="high"
    />
  );
};

export const EventImage: React.FC<Omit<EnhancedImageLoaderProps, 'fallbackSrc'> & { eventType?: string }> = ({ 
  eventType, 
  ...props 
}) => {
  const fallbackSrc = eventType ? placeholderImages[`${eventType}Placeholder` as keyof typeof placeholderImages] : undefined;
  
  return (
    <EnhancedImageLoader
      {...props}
      fallbackSrc={fallbackSrc}
      quality="high"
      overlay={true}
      overlayColor="rgba(0, 0, 0, 0.2)"
    />
  );
};

export const BackgroundImage: React.FC<EnhancedImageLoaderProps> = (props) => {
  return (
    <EnhancedImageLoader
      {...props}
      quality="ultra"
      lazy={false}
      blur={true}
      className={`${props.className} absolute inset-0`}
    />
  );
};

export default EnhancedImageLoader;
