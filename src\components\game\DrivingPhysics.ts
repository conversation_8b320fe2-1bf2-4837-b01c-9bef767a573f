/**
 * Basic driving physics system for Aussie Burnout Legends
 */

export interface VehicleState {
  // Position and orientation
  positionX: number;
  positionY: number;
  rotation: number; // in radians
  
  // Movement
  speed: number;
  acceleration: number;
  angularVelocity: number;
  
  // Vehicle properties
  maxSpeed: number;
  accelerationRate: number;
  brakeForce: number;
  handling: number;
  driftFactor: number;
  burnoutThreshold: number;
  
  // State flags
  isBraking: boolean;
  isDrifting: boolean;
  isBurningOut: boolean;
  
  // Tire state (for burnouts)
  rearTireHealth: number;
  rearTireSmoke: number;
  
  // Performance stats
  currentRPM: number;
  gear: number;
}

export interface VehicleControls {
  throttle: number; // 0 to 1
  brake: number; // 0 to 1
  steering: number; // -1 to 1
  handbrake: boolean;
}

export interface VehicleConfig {
  maxSpeed: number;
  accelerationRate: number;
  brakeForce: number;
  handling: number;
  driftFactor: number;
  weight: number;
  burnoutThreshold: number;
  maxRPM: number;
  gearRatios: number[];
}

// Default configuration for the RX-3
export const DEFAULT_RX3_CONFIG: VehicleConfig = {
  maxSpeed: 180, // km/h
  accelerationRate: 5,
  brakeForce: 8,
  handling: 0.7,
  driftFactor: 0.4,
  weight: 1000, // kg
  burnoutThreshold: 0.8, // 80% throttle while stationary
  maxRPM: 8000,
  gearRatios: [0, 3.5, 2.5, 1.8, 1.3, 1.0], // 0 is reverse
};

// Initialize a new vehicle state
export function initVehicleState(config: VehicleConfig = DEFAULT_RX3_CONFIG): VehicleState {
  return {
    positionX: 0,
    positionY: 0,
    rotation: 0,
    
    speed: 0,
    acceleration: 0,
    angularVelocity: 0,
    
    maxSpeed: config.maxSpeed,
    accelerationRate: config.accelerationRate,
    brakeForce: config.brakeForce,
    handling: config.handling,
    driftFactor: config.driftFactor,
    burnoutThreshold: config.burnoutThreshold,
    
    isBraking: false,
    isDrifting: false,
    isBurningOut: false,
    
    rearTireHealth: 100,
    rearTireSmoke: 0,
    
    currentRPM: 0,
    gear: 1,
  };
}

// Update vehicle state based on controls and time delta
export function updateVehiclePhysics(
  state: VehicleState, 
  controls: VehicleControls, 
  deltaTime: number
): VehicleState {
  // Create a new state object to avoid mutating the original
  const newState = { ...state };
  
  // Handle throttle input
  if (controls.throttle > 0) {
    // Calculate acceleration based on throttle input
    newState.acceleration = controls.throttle * newState.accelerationRate;
    
    // Check for burnout condition
    if (newState.speed < 5 && controls.throttle > newState.burnoutThreshold && (controls.handbrake || controls.brake > 0.5)) {
      newState.isBurningOut = true;
      newState.rearTireSmoke = Math.min(100, newState.rearTireSmoke + controls.throttle * 10 * deltaTime);
      newState.rearTireHealth = Math.max(0, newState.rearTireHealth - controls.throttle * 5 * deltaTime);
      
      // Limit forward acceleration during burnout
      newState.acceleration *= 0.2;
    } else {
      newState.isBurningOut = false;
      newState.rearTireSmoke = Math.max(0, newState.rearTireSmoke - 20 * deltaTime);
    }
  } else {
    // Deceleration when no throttle
    newState.acceleration = -0.5;
    newState.isBurningOut = false;
    newState.rearTireSmoke = Math.max(0, newState.rearTireSmoke - 20 * deltaTime);
  }
  
  // Handle braking
  if (controls.brake > 0) {
    newState.isBraking = true;
    newState.acceleration -= controls.brake * newState.brakeForce;
  } else {
    newState.isBraking = false;
  }
  
  // Update speed based on acceleration
  newState.speed += newState.acceleration * deltaTime;
  
  // Clamp speed between 0 and max speed
  newState.speed = Math.max(0, Math.min(newState.maxSpeed, newState.speed));
  
  // Handle steering and calculate angular velocity
  const steeringFactor = controls.steering * newState.handling;
  
  // Steering is more effective at lower speeds
  const speedFactor = 1 - (newState.speed / newState.maxSpeed) * 0.5;
  
  // Calculate base angular velocity
  newState.angularVelocity = steeringFactor * speedFactor * 2;
  
  // Apply drift effect if handbrake is active or during hard turns at speed
  if (controls.handbrake || (Math.abs(controls.steering) > 0.7 && newState.speed > newState.maxSpeed * 0.6)) {
    newState.isDrifting = true;
    newState.angularVelocity *= 1 + newState.driftFactor;
    
    // Reduce speed during drift
    newState.speed *= 0.98;
    
    // Generate some smoke during drifting
    newState.rearTireSmoke = Math.min(100, newState.rearTireSmoke + 5 * deltaTime);
  } else {
    newState.isDrifting = false;
  }
  
  // Update rotation based on angular velocity
  newState.rotation += newState.angularVelocity * deltaTime;
  
  // Update position based on speed and rotation
  const moveX = Math.sin(newState.rotation) * newState.speed * deltaTime;
  const moveY = Math.cos(newState.rotation) * newState.speed * deltaTime;
  
  newState.positionX += moveX;
  newState.positionY += moveY;
  
  // Update RPM and gear
  updateRPMAndGear(newState, controls);
  
  return newState;
}

// Helper function to update RPM and gear
function updateRPMAndGear(state: VehicleState, controls: VehicleControls): void {
  // Calculate target RPM based on speed and current gear
  const speedFactor = state.speed / state.maxSpeed;
  const gearFactor = state.gear > 0 ? DEFAULT_RX3_CONFIG.gearRatios[state.gear] : DEFAULT_RX3_CONFIG.gearRatios[1];
  
  let targetRPM = speedFactor * DEFAULT_RX3_CONFIG.maxRPM * gearFactor;
  
  // Add throttle influence
  if (controls.throttle > 0 && state.speed < 5) {
    // Engine revving when stationary
    targetRPM = controls.throttle * DEFAULT_RX3_CONFIG.maxRPM * 0.8;
  } else if (state.isBurningOut) {
    // High RPM during burnout
    targetRPM = DEFAULT_RX3_CONFIG.maxRPM * 0.9;
  }
  
  // Smoothly interpolate current RPM towards target
  state.currentRPM = state.currentRPM + (targetRPM - state.currentRPM) * 0.1;
  
  // Auto gear shifting (simplified)
  if (state.currentRPM > DEFAULT_RX3_CONFIG.maxRPM * 0.85 && state.gear < DEFAULT_RX3_CONFIG.gearRatios.length - 1) {
    state.gear++;
    state.currentRPM *= 0.6; // RPM drops after shifting up
  } else if (state.currentRPM < DEFAULT_RX3_CONFIG.maxRPM * 0.3 && state.gear > 1) {
    state.gear--;
    state.currentRPM *= 1.5; // RPM increases after shifting down
  }
}

// Calculate score for burnouts
export function calculateBurnoutScore(state: VehicleState): number {
  // Score factors:
  // 1. Amount of smoke
  // 2. Duration of burnout
  // 3. Tire health management (not completely destroying tires)
  
  const smokeScore = state.rearTireSmoke * 2;
  const healthFactor = state.rearTireHealth > 20 ? 1.5 : 0.5; // Bonus for maintaining some tire health
  
  return Math.round(smokeScore * healthFactor);
}

// Calculate score for drifting
export function calculateDriftScore(state: VehicleState, driftAngle: number, driftDuration: number): number {
  // Score factors:
  // 1. Drift angle
  // 2. Speed during drift
  // 3. Duration of drift
  
  const angleScore = Math.abs(driftAngle) * 10;
  const speedScore = (state.speed / state.maxSpeed) * 100;
  const durationScore = driftDuration * 5;
  
  return Math.round(angleScore + speedScore + durationScore);
}
