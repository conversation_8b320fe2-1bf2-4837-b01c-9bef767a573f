import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  foundersEdition: boolean;
  registrationDate: string;
  totalScore: number;
  achievements: string[];
  garage: CarData[];
  preferences: UserPreferences;
}

interface CarData {
  id: string;
  name: string;
  model: string;
  customization: {
    bodyColor: string;
    hasRacingStripes: boolean;
    hasAustralianFlag: boolean;
    wheelSize: number;
    exhaustType: string;
  };
  stats: {
    power: number;
    handling: number;
    acceleration: number;
    braking: number;
  };
  unlocked: boolean;
  purchaseDate?: string;
}

interface UserPreferences {
  soundEnabled: boolean;
  graphicsQuality: 'low' | 'medium' | 'high';
  controlScheme: 'wasd' | 'arrows' | 'custom';
  notifications: boolean;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (username: string, email: string, password: string) => Promise<boolean>;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
  addCarToGarage: (car: CarData) => void;
  updateCarCustomization: (carId: string, customization: CarData['customization']) => void;
  unlockAchievement: (achievementId: string) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { toast } = useToast();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initAuth = () => {
      try {
        const savedUser = localStorage.getItem('aussie-burnout-user');
        if (savedUser) {
          setUser(JSON.parse(savedUser));
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        localStorage.removeItem('aussie-burnout-user');
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  // Save user data to localStorage whenever user changes
  useEffect(() => {
    if (user) {
      localStorage.setItem('aussie-burnout-user', JSON.stringify(user));
    } else {
      localStorage.removeItem('aussie-burnout-user');
    }
  }, [user]);

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if user exists in localStorage (simulated database)
      const existingUsers = JSON.parse(localStorage.getItem('aussie-burnout-users') || '[]');
      const foundUser = existingUsers.find((u: any) => u.email === email && u.password === password);
      
      if (foundUser) {
        const { password: _, ...userWithoutPassword } = foundUser;
        setUser(userWithoutPassword);
        
        toast({
          title: "Welcome back!",
          description: `G'day ${foundUser.username}! Ready to burn some rubber?`,
          className: "bg-racing-black border-racing-yellow text-white",
        });
        
        return true;
      } else {
        toast({
          title: "Login Failed",
          description: "Invalid email or password. Please try again.",
          variant: "destructive",
        });
        return false;
      }
    } catch (error) {
      toast({
        title: "Login Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (username: string, email: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if user already exists
      const existingUsers = JSON.parse(localStorage.getItem('aussie-burnout-users') || '[]');
      const userExists = existingUsers.some((u: any) => u.email === email || u.username === username);
      
      if (userExists) {
        toast({
          title: "Registration Failed",
          description: "User with this email or username already exists.",
          variant: "destructive",
        });
        return false;
      }
      
      // Create new user with default car
      const newUser: User = {
        id: Date.now().toString(),
        username,
        email,
        foundersEdition: existingUsers.length < 100,
        registrationDate: new Date().toISOString(),
        totalScore: 0,
        achievements: ['welcome'],
        garage: [{
          id: 'rx3-founders',
          name: 'Founders Edition RX-3',
          model: 'Mazda RX-3 Savanna',
          customization: {
            bodyColor: '#FF0000',
            hasRacingStripes: false,
            hasAustralianFlag: true,
            wheelSize: 17,
            exhaustType: 'standard'
          },
          stats: { power: 85, handling: 75, acceleration: 80, braking: 70 },
          unlocked: true,
          purchaseDate: new Date().toISOString()
        }],
        preferences: {
          soundEnabled: true,
          graphicsQuality: 'medium',
          controlScheme: 'wasd',
          notifications: true
        }
      };
      
      // Save to simulated database
      const updatedUsers = [...existingUsers, { ...newUser, password }];
      localStorage.setItem('aussie-burnout-users', JSON.stringify(updatedUsers));
      
      setUser(newUser);
      
      toast({
        title: "Welcome to Aussie Burnout Legends!",
        description: newUser.foundersEdition 
          ? "Congratulations! You've received the exclusive Founders Edition RX-3!" 
          : "Your garage has been set up with a classic RX-3!",
        className: "bg-racing-black border-racing-yellow text-white",
      });
      
      return true;
    } catch (error) {
      toast({
        title: "Registration Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    toast({
      title: "Logged Out",
      description: "See you on the track soon!",
      className: "bg-racing-black border-racing-yellow text-white",
    });
  };

  const updateUser = (updates: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...updates });
    }
  };

  const addCarToGarage = (car: CarData) => {
    if (user) {
      const updatedUser = {
        ...user,
        garage: [...user.garage, car]
      };
      setUser(updatedUser);
      
      toast({
        title: "New Car Added!",
        description: `${car.name} has been added to your garage!`,
        className: "bg-racing-black border-racing-yellow text-white",
      });
    }
  };

  const updateCarCustomization = (carId: string, customization: CarData['customization']) => {
    if (user) {
      const updatedGarage = user.garage.map(car => 
        car.id === carId ? { ...car, customization } : car
      );
      setUser({ ...user, garage: updatedGarage });
    }
  };

  const unlockAchievement = (achievementId: string) => {
    if (user && !user.achievements.includes(achievementId)) {
      const updatedUser = {
        ...user,
        achievements: [...user.achievements, achievementId]
      };
      setUser(updatedUser);
      
      // Show achievement notification
      toast({
        title: "Achievement Unlocked!",
        description: getAchievementName(achievementId),
        className: "bg-racing-black border-racing-yellow text-white",
      });
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    updateUser,
    addCarToGarage,
    updateCarCustomization,
    unlockAchievement
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Helper function to get achievement names
const getAchievementName = (achievementId: string): string => {
  const achievements: Record<string, string> = {
    welcome: "Welcome to the Family!",
    first_burnout: "Smoke 'Em If You Got 'Em",
    first_drift: "Sideways Sensation",
    speed_demon: "Speed Demon",
    customizer: "Style Master",
    perfectionist: "Perfectionist",
    legend: "Aussie Legend"
  };
  
  return achievements[achievementId] || "Unknown Achievement";
};

export type { User, CarData, UserPreferences };
