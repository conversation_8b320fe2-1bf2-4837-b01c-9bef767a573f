
import React from 'react';
import { Button } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';

const NotFound = () => {
  const navigate = useNavigate();
  
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-gray-900 to-black p-4">
      <div className="max-w-md w-full text-center space-y-6">
        <div className="space-y-2">
          <h1 className="text-6xl font-bold text-racing-yellow">404</h1>
          <h2 className="text-3xl font-bold text-racing-red">Burnout Fail!</h2>
          <p className="text-xl text-gray-400">
            Looks like you've spun out and gone off track.
          </p>
        </div>
        
        <div className="py-8">
          <div className="relative mx-auto w-48 h-36">
            <div className="absolute inset-0 flex items-center justify-center">
              {/* Simple car silhouette */}
              <svg className="w-32 h-24 text-racing-red opacity-50" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path fill="currentColor" d="M19 17h2v-5h-2V8.5C19 7.13 17.87 6 16.5 6h-9C6.13 6 5 7.13 5 8.5V12H3v5h2c0 1.66 1.34 3 3 3s3-1.34 3-3h4c0 1.66 1.34 3 3 3s3-1.34 3-3M7.25 8.5h9.5c.28 0 .5.22.5.5H6.75c0-.28.22-.5.5-.5M8 17c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m8 0c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m-5-3V7h5v2l3 3v2H11z" />
              </svg>
            </div>
            
            {/* Tire smoke effect */}
            <div className="absolute bottom-0 left-0 right-0">
              <div className="flex justify-center">
                <div className="w-4 h-4 bg-white rounded-full opacity-20 animate-burnout-smoke"></div>
                <div className="w-4 h-4 bg-white rounded-full opacity-30 animate-burnout-smoke" style={{ animationDelay: '0.5s' }}></div>
                <div className="w-4 h-4 bg-white rounded-full opacity-25 animate-burnout-smoke" style={{ animationDelay: '1s' }}></div>
                <div className="w-4 h-4 bg-white rounded-full opacity-15 animate-burnout-smoke" style={{ animationDelay: '1.5s' }}></div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <p className="text-gray-300">
            The page you're looking for has spun out of existence or is doing a burnout somewhere else.
          </p>
          
          <Button onClick={() => navigate('/')} className="bg-racing-red hover:bg-red-700 text-white font-bold py-3 px-6">
            Back to Pit Lane
          </Button>
        </div>
      </div>
      
      {/* Tire tracks */}
      <div className="absolute bottom-0 left-0 right-0 h-16 opacity-10 tire-track"></div>
    </div>
  );
};

export default NotFound;
