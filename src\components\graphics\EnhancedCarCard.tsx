import React, { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { motion, AnimatePresence } from 'framer-motion';
import {
  Star,
  Crown,
  Lock,
  Zap,
  Gauge,
  Settings,
  Eye,
  Heart,
  Share2,
  MoreHorizontal
} from 'lucide-react';
import { placeholderImages, carImages } from '@/assets/images/cars';
import { CarImage } from '@/components/graphics/EnhancedImageLoader';
import { useGraphics } from '@/components/graphics/GraphicsOptimizer';

interface CarStats {
  power: number;
  handling: number;
  acceleration: number;
  braking: number;
  topSpeed: number;
  weight: number;
}

interface EnhancedCarCardProps {
  id: string;
  name: string;
  model: string;
  year: string;
  image?: string;
  stats: CarStats;
  rarity: 'common' | 'uncommon' | 'rare' | 'legendary';
  price?: number;
  owned?: boolean;
  locked?: boolean;
  foundersEdition?: boolean;
  onSelect?: () => void;
  onCustomize?: () => void;
  onFavorite?: () => void;
  className?: string;
}

const EnhancedCarCard: React.FC<EnhancedCarCardProps> = ({
  id,
  name,
  model,
  year,
  image,
  stats,
  rarity,
  price,
  owned = false,
  locked = false,
  foundersEdition = false,
  onSelect,
  onCustomize,
  onFavorite,
  className
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const { shouldRenderEffect, getOptimizedImageUrl } = useGraphics();

  const getRarityColor = () => {
    switch (rarity) {
      case 'common': return 'from-gray-500 to-gray-600';
      case 'uncommon': return 'from-green-500 to-green-600';
      case 'rare': return 'from-blue-500 to-blue-600';
      case 'legendary': return 'from-purple-500 via-pink-500 to-yellow-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const getRarityBorder = () => {
    switch (rarity) {
      case 'common': return 'border-gray-500/30';
      case 'uncommon': return 'border-green-500/30';
      case 'rare': return 'border-blue-500/30';
      case 'legendary': return 'border-purple-500/30';
      default: return 'border-gray-500/30';
    }
  };

  const getStatColor = (value: number) => {
    if (value >= 90) return 'from-green-400 to-green-600';
    if (value >= 70) return 'from-yellow-400 to-yellow-600';
    if (value >= 50) return 'from-orange-400 to-orange-600';
    return 'from-red-400 to-red-600';
  };

  // Get the appropriate car image
  const getCarImage = () => {
    if (image) return image;

    // Map car names to image keys
    const carImageMap: { [key: string]: string } = {
      'rx-3': carImages.rx3.main,
      'rx3': carImages.rx3.main,
      'mazda': carImages.rx3.main,
      'commodore': carImages.commodore.main,
      'holden': carImages.commodore.main,
      'falcon': carImages.falcon.main,
      'ford': carImages.falcon.main,
      'torana': carImages.torana.main
    };

    const carKey = name.toLowerCase().replace(/[^a-z0-9]/g, '');
    for (const [key, imageUrl] of Object.entries(carImageMap)) {
      if (carKey.includes(key)) {
        return imageUrl;
      }
    }

    return carImages.rx3.main; // Default fallback
  };

  const cardImage = getCarImage();

  return (
    <motion.div
      className={`relative ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className={`
        relative overflow-hidden bg-gradient-to-br from-gray-900/90 to-black/90
        backdrop-blur-sm border-2 ${getRarityBorder()}
        ${locked ? 'opacity-70' : ''}
        ${isHovered ? 'shadow-2xl shadow-racing-yellow/20' : 'shadow-lg'}
        transition-all duration-300
      `}>
        {/* Rarity Glow Effect */}
        {rarity === 'legendary' && (
          <div className={`absolute inset-0 bg-gradient-to-r ${getRarityColor()} opacity-10 animate-pulse`} />
        )}

        {/* Image Section */}
        <div className="relative h-48 overflow-hidden">
          <CarImage
            src={getOptimizedImageUrl(cardImage, 400, 300)}
            alt={name}
            carModel={name.toLowerCase().includes('rx') ? 'rx3' :
                     name.toLowerCase().includes('commodore') ? 'commodore' :
                     name.toLowerCase().includes('falcon') ? 'falcon' :
                     name.toLowerCase().includes('torana') ? 'torana' : 'rx3'}
            width={400}
            height={300}
            quality="high"
            className={`w-full h-full transition-all duration-500 ${
              locked ? 'filter blur-sm grayscale' : ''
            } ${shouldRenderEffect('animations') && isHovered ? 'scale-110' : 'scale-100'}`}
            onLoad={() => setImageLoaded(true)}
          />

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent" />

          {/* Top Badges */}
          <div className="absolute top-4 left-4 flex flex-col gap-2">
            {foundersEdition && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3, type: "spring" }}
              >
                <Badge className={`bg-gradient-to-r ${getRarityColor()} text-white font-bold px-3 py-1`}>
                  <Crown className="w-3 h-3 mr-1" />
                  Founders Edition
                </Badge>
              </motion.div>
            )}

            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.4, type: "spring" }}
            >
              <Badge className={`bg-gradient-to-r ${getRarityColor()} text-white capitalize px-2 py-1`}>
                {rarity}
              </Badge>
            </motion.div>
          </div>

          {/* Top Right Actions */}
          <div className="absolute top-4 right-4 flex gap-2">
            <motion.button
              className="p-2 bg-black/50 backdrop-blur-sm rounded-full text-white hover:bg-racing-yellow hover:text-black transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onFavorite}
            >
              <Heart className="w-4 h-4" />
            </motion.button>

            <motion.button
              className="p-2 bg-black/50 backdrop-blur-sm rounded-full text-white hover:bg-racing-yellow hover:text-black transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Share2 className="w-4 h-4" />
            </motion.button>
          </div>

          {/* Lock Overlay */}
          {locked && (
            <motion.div
              className="absolute inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <div className="text-center">
                <motion.div
                  className="p-4 rounded-full bg-gray-800/80 border-2 border-gray-600 mb-4"
                  animate={{ rotate: [0, 5, -5, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <Lock className="w-8 h-8 text-gray-400" />
                </motion.div>
                <p className="text-white font-semibold">Locked</p>
                <p className="text-gray-400 text-sm">Complete challenges to unlock</p>
              </div>
            </motion.div>
          )}

          {/* Bottom Info */}
          <div className="absolute bottom-4 left-4 right-4">
            <h3 className="text-white font-bold text-lg mb-1">{name}</h3>
            <div className="flex justify-between items-center">
              <span className="text-racing-yellow font-semibold">{year}</span>
              {price && (
                <span className="text-green-400 font-bold">${price.toLocaleString()}</span>
              )}
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <CardContent className="p-4 space-y-3">
          <AnimatePresence>
            {(isHovered || showDetails) && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-3"
              >
                {Object.entries(stats).map(([stat, value], index) => (
                  <motion.div
                    key={stat}
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="space-y-1"
                  >
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-300 capitalize">
                        {stat.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                      <span className="text-white font-semibold">{value}</span>
                    </div>
                    <div className="relative h-2 bg-gray-700 rounded-full overflow-hidden">
                      <motion.div
                        className={`h-full bg-gradient-to-r ${getStatColor(value)} rounded-full`}
                        initial={{ width: 0 }}
                        animate={{ width: `${value}%` }}
                        transition={{ delay: index * 0.1 + 0.2, duration: 0.8 }}
                      />
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            {owned ? (
              <>
                <Button
                  onClick={onSelect}
                  className="flex-1 bg-racing-red hover:bg-red-700 text-white"
                  disabled={locked}
                >
                  <Gauge className="w-4 h-4 mr-2" />
                  Select
                </Button>
                <Button
                  onClick={onCustomize}
                  variant="outline"
                  className="flex-1"
                  disabled={locked}
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Customize
                </Button>
              </>
            ) : (
              <Button
                onClick={onSelect}
                className="flex-1 bg-racing-yellow hover:bg-yellow-600 text-black font-semibold"
                disabled={locked}
              >
                {locked ? (
                  <>
                    <Lock className="w-4 h-4 mr-2" />
                    Unlock
                  </>
                ) : (
                  <>
                    <Zap className="w-4 h-4 mr-2" />
                    Purchase
                  </>
                )}
              </Button>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="px-3"
            >
              {showDetails ? <MoreHorizontal className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
          </div>
        </CardContent>

        {/* Hover Glow Effect */}
        <AnimatePresence>
          {isHovered && (
            <motion.div
              className={`absolute inset-0 bg-gradient-to-r ${getRarityColor()} opacity-20 pointer-events-none`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.2 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            />
          )}
        </AnimatePresence>
      </Card>
    </motion.div>
  );
};

export default EnhancedCarCard;
