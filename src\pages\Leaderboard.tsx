
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

interface Player {
  id: number;
  rank: number;
  name: string;
  avatar: string;
  score: number;
  wins: number;
  isFounder?: boolean;
  car?: string;
  country?: string;
}

const Leaderboard = () => {
  const players: Record<string, Player[]> = {
    overall: [
      { id: 1, rank: 1, name: "BurnoutKing", avatar: "B", score: 9850, wins: 37, isFounder: true, car: "Mazda RX-3 Savanna", country: "AU" },
      { id: 2, rank: 2, name: "ToranaLegend", avatar: "T", score: 9340, wins: 32, car: "Holden Torana SLR 5000", country: "AU" },
      { id: 3, rank: 3, name: "SmokeScreen", avatar: "S", score: 8790, wins: 28, isFounder: true, car: "Ford Falcon XY GT", country: "AU" },
      { id: 4, rank: 4, name: "RotaryPower", avatar: "R", score: 8450, wins: 25, car: "Mazda RX-7", country: "AU" },
      { id: 5, rank: 5, name: "BurnoutQueen", avatar: "B", score: 7980, wins: 23, car: "Holden Monaro", country: "NZ" },
      { id: 6, rank: 6, name: "TireFryer", avatar: "T", score: 7650, wins: 21, isFounder: true, car: "Mazda RX-3 Savanna", country: "AU" },
      { id: 7, rank: 7, name: "V8Monster", avatar: "V", score: 7340, wins: 19, car: "Ford Falcon XB", country: "AU" },
      { id: 8, rank: 8, name: "DriftKing", avatar: "D", score: 6890, wins: 17, car: "Nissan Silvia", country: "AU" },
      { id: 9, rank: 9, name: "RubberBurner", avatar: "R", score: 6540, wins: 15, car: "Holden Commodore VK", country: "AU" },
      { id: 10, rank: 10, name: "SkidMark", avatar: "S", score: 6120, wins: 13, isFounder: true, car: "Mazda RX-3 Savanna", country: "AU" }
    ],
    burnout: [
      { id: 101, rank: 1, name: "SmokeScreen", avatar: "S", score: 4350, wins: 18, isFounder: true, car: "Ford Falcon XY GT", country: "AU" },
      { id: 102, rank: 2, name: "BurnoutKing", avatar: "B", score: 4120, wins: 15, isFounder: true, car: "Mazda RX-3 Savanna", country: "AU" },
      { id: 103, rank: 3, name: "TireFryer", avatar: "T", score: 3980, wins: 14, isFounder: true, car: "Mazda RX-3 Savanna", country: "AU" },
      { id: 104, rank: 4, name: "RubberBurner", avatar: "R", score: 3750, wins: 12, car: "Holden Commodore VK", country: "AU" },
      { id: 105, rank: 5, name: "SkidMark", avatar: "S", score: 3540, wins: 10, isFounder: true, car: "Mazda RX-3 Savanna", country: "AU" }
    ],
    drag: [
      { id: 201, rank: 1, name: "BurnoutKing", avatar: "B", score: 3850, wins: 17, isFounder: true, car: "Mazda RX-3 Savanna", country: "AU" },
      { id: 202, rank: 2, name: "RotaryPower", avatar: "R", score: 3650, wins: 15, car: "Mazda RX-7", country: "AU" },
      { id: 203, rank: 3, name: "ToranaLegend", avatar: "T", score: 3520, wins: 14, car: "Holden Torana SLR 5000", country: "AU" },
      { id: 204, rank: 4, name: "V8Monster", avatar: "V", score: 3340, wins: 12, car: "Ford Falcon XB", country: "AU" },
      { id: 205, rank: 5, name: "DriftKing", avatar: "D", score: 3120, wins: 10, car: "Nissan Silvia", country: "AU" }
    ],
    drift: [
      { id: 301, rank: 1, name: "DriftKing", avatar: "D", score: 4250, wins: 16, car: "Nissan Silvia", country: "AU" },
      { id: 302, rank: 2, name: "BurnoutQueen", avatar: "B", score: 4050, wins: 15, car: "Holden Monaro", country: "NZ" },
      { id: 303, rank: 3, name: "RotaryPower", avatar: "R", score: 3870, wins: 13, car: "Mazda RX-7", country: "AU" },
      { id: 304, rank: 4, name: "TireFryer", avatar: "T", score: 3680, wins: 12, isFounder: true, car: "Mazda RX-3 Savanna", country: "AU" },
      { id: 305, rank: 5, name: "SkidMark", avatar: "S", score: 3450, wins: 10, isFounder: true, car: "Mazda RX-3 Savanna", country: "AU" }
    ]
  };

  const renderLeaderboard = (players: Player[]) => {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-12 gap-4 text-sm text-gray-400 px-4 py-2">
          <div className="col-span-1 font-medium">Rank</div>
          <div className="col-span-4 font-medium">Player</div>
          <div className="col-span-2 font-medium text-right">Score</div>
          <div className="col-span-1 font-medium text-right">Wins</div>
          <div className="col-span-4 font-medium hidden md:block">Car</div>
        </div>
        
        <Separator className="bg-gray-800" />
        
        <div className="space-y-2">
          {players.map((player) => (
            <div 
              key={player.id}
              className={`grid grid-cols-12 gap-4 items-center p-3 rounded-lg ${
                player.rank === 1 
                  ? 'bg-racing-yellow/10 border border-racing-yellow/30' 
                  : player.rank <= 3 
                    ? 'bg-racing-red/10 border border-racing-red/30' 
                    : 'bg-gray-900'
              }`}
            >
              <div className="col-span-1 font-bold text-lg">
                {player.rank === 1 && <span className="text-racing-yellow">#1</span>}
                {player.rank === 2 && <span className="text-gray-300">#2</span>}
                {player.rank === 3 && <span className="text-amber-700">#3</span>}
                {player.rank > 3 && <span className="text-gray-400">#{player.rank}</span>}
              </div>
              
              <div className="col-span-4 flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${
                  player.isFounder ? 'bg-gradient-to-br from-racing-yellow to-racing-red text-black' : 'bg-gray-800 text-white'
                }`}>
                  {player.avatar}
                </div>
                <div>
                  <div className="font-medium flex items-center gap-2">
                    {player.name}
                    {player.isFounder && (
                      <Badge variant="outline" className="text-[10px] px-1 py-0 h-4 bg-racing-yellow/20 text-racing-yellow border-racing-yellow/30">
                        Founder
                      </Badge>
                    )}
                  </div>
                  <div className="text-xs text-gray-500 flex items-center">
                    {player.country === 'AU' ? '🇦🇺' : player.country === 'NZ' ? '🇳🇿' : '🌍'} {player.country}
                  </div>
                </div>
              </div>
              
              <div className="col-span-2 text-right font-bold">
                {player.rank === 1 && <span className="text-racing-yellow">{player.score.toLocaleString()}</span>}
                {player.rank > 1 && <span>{player.score.toLocaleString()}</span>}
              </div>
              
              <div className="col-span-1 text-right font-medium text-gray-300">
                {player.wins}
              </div>
              
              <div className="col-span-4 text-gray-400 hidden md:block overflow-hidden text-ellipsis whitespace-nowrap">
                {player.car}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow py-16 bg-gradient-to-b from-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-racing-yellow mb-4">Leaderboard</h1>
            <p className="text-gray-300">
              Check out who's burning rubber at the top of the rankings. 
              Compete in events to climb the leaderboard and become an Aussie Burnout Legend!
            </p>
          </div>
          
          <Tabs defaultValue="overall" className="w-full">
            <TabsList className="w-full grid grid-cols-4 max-w-2xl mb-8">
              <TabsTrigger value="overall">Overall</TabsTrigger>
              <TabsTrigger value="burnout">Burnout</TabsTrigger>
              <TabsTrigger value="drag">Drag</TabsTrigger>
              <TabsTrigger value="drift">Drift</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overall">
              <Card className="bg-black/40 border-racing-yellow/20">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-2xl text-racing-yellow">Overall Champions</CardTitle>
                    <Badge className="bg-racing-red border-none">Season 1</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  {renderLeaderboard(players.overall)}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="burnout">
              <Card className="bg-black/40 border-racing-yellow/20">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-2xl text-racing-yellow">Burnout Kings</CardTitle>
                    <Badge className="bg-racing-red border-none">Season 1</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  {renderLeaderboard(players.burnout)}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="drag">
              <Card className="bg-black/40 border-racing-yellow/20">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-2xl text-racing-yellow">Drag Racing Elite</CardTitle>
                    <Badge className="bg-racing-red border-none">Season 1</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  {renderLeaderboard(players.drag)}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="drift">
              <Card className="bg-black/40 border-racing-yellow/20">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-2xl text-racing-yellow">Drift Masters</CardTitle>
                    <Badge className="bg-racing-red border-none">Season 1</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  {renderLeaderboard(players.drift)}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Leaderboard;
