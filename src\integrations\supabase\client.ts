// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://vynvkwqohhzcxeckdatb.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ5bnZrd3FvaGh6Y3hlY2tkYXRiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYxMDA2MTMsImV4cCI6MjA2MTY3NjYxM30.Vf36rOaHi2hSutcUupaH5kGkK2u68gt9L1itFBYpH-4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);