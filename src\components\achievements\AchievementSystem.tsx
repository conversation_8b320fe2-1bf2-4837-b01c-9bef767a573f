import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Trophy, Star, Target, Zap, Wrench, Crown } from 'lucide-react';
import AustralianEmblem from '@/components/AustralianEmblem';
import { useAuth } from '@/context/AuthContext';
import EnhancedAchievementBadge from '@/components/graphics/EnhancedAchievementBadge';
import { motion } from 'framer-motion';

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: 'driving' | 'customization' | 'social' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  points: number;
  requirements: {
    type: string;
    target: number;
    current?: number;
  };
  unlocked: boolean;
  unlockedDate?: string;
  reward?: {
    type: 'car' | 'customization' | 'title' | 'badge';
    value: string;
  };
}

const ACHIEVEMENTS: Achievement[] = [
  {
    id: 'welcome',
    name: 'Welcome to the Family!',
    description: 'Register your account and join the Aussie Burnout Legends community',
    icon: <Crown className="w-6 h-6" />,
    category: 'special',
    rarity: 'common',
    points: 10,
    requirements: { type: 'register', target: 1 },
    unlocked: false
  },
  {
    id: 'first_burnout',
    name: "Smoke 'Em If You Got 'Em",
    description: 'Complete your first burnout challenge',
    icon: <Zap className="w-6 h-6" />,
    category: 'driving',
    rarity: 'common',
    points: 25,
    requirements: { type: 'burnout_score', target: 100 },
    unlocked: false
  },
  {
    id: 'first_drift',
    name: 'Sideways Sensation',
    description: 'Complete your first drift challenge',
    icon: <Target className="w-6 h-6" />,
    category: 'driving',
    rarity: 'common',
    points: 25,
    requirements: { type: 'drift_score', target: 100 },
    unlocked: false
  },
  {
    id: 'speed_demon',
    name: 'Speed Demon',
    description: 'Reach maximum speed in the driving simulator',
    icon: <Zap className="w-6 h-6" />,
    category: 'driving',
    rarity: 'rare',
    points: 50,
    requirements: { type: 'max_speed', target: 180 },
    unlocked: false
  },
  {
    id: 'customizer',
    name: 'Style Master',
    description: 'Customize your car with all available options',
    icon: <Wrench className="w-6 h-6" />,
    category: 'customization',
    rarity: 'rare',
    points: 75,
    requirements: { type: 'customizations', target: 10 },
    unlocked: false
  },
  {
    id: 'perfectionist',
    name: 'Perfectionist',
    description: 'Score over 5000 points in a single game session',
    icon: <Star className="w-6 h-6" />,
    category: 'driving',
    rarity: 'epic',
    points: 100,
    requirements: { type: 'single_session_score', target: 5000 },
    unlocked: false
  },
  {
    id: 'legend',
    name: 'Aussie Legend',
    description: 'Reach a total score of 50,000 points',
    icon: <Trophy className="w-6 h-6" />,
    category: 'driving',
    rarity: 'legendary',
    points: 500,
    requirements: { type: 'total_score', target: 50000 },
    unlocked: false,
    reward: {
      type: 'car',
      value: 'Holden Commodore VL Turbo'
    }
  },
  {
    id: 'founders_edition',
    name: 'Founding Father',
    description: 'One of the first 100 players to join Aussie Burnout Legends',
    icon: <Crown className="w-6 h-6" />,
    category: 'special',
    rarity: 'legendary',
    points: 1000,
    requirements: { type: 'founders_edition', target: 1 },
    unlocked: false,
    reward: {
      type: 'title',
      value: 'Founding Father'
    }
  }
];

interface AchievementSystemProps {
  className?: string;
}

const AchievementSystem: React.FC<AchievementSystemProps> = ({ className }) => {
  const { user } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Update achievements based on user data
  const userAchievements = ACHIEVEMENTS.map(achievement => ({
    ...achievement,
    unlocked: user?.achievements.includes(achievement.id) || false,
    unlockedDate: user?.achievements.includes(achievement.id) ? user.registrationDate : undefined,
    requirements: {
      ...achievement.requirements,
      current: getCurrentProgress(achievement, user)
    }
  }));

  const filteredAchievements = selectedCategory === 'all'
    ? userAchievements
    : userAchievements.filter(a => a.category === selectedCategory);

  const unlockedCount = userAchievements.filter(a => a.unlocked).length;
  const totalPoints = userAchievements
    .filter(a => a.unlocked)
    .reduce((sum, a) => sum + a.points, 0);

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-500';
      case 'rare': return 'bg-blue-500';
      case 'epic': return 'bg-purple-500';
      case 'legendary': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'driving': return <Zap className="w-4 h-4" />;
      case 'customization': return <Wrench className="w-4 h-4" />;
      case 'social': return <Star className="w-4 h-4" />;
      case 'special': return <Crown className="w-4 h-4" />;
      default: return <Trophy className="w-4 h-4" />;
    }
  };

  return (
    <div className={`${className}`}>
      <Card className="bg-black/40 border-racing-yellow/20">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl text-racing-yellow">Achievements</CardTitle>
              <CardDescription className="text-gray-400">
                Track your progress and unlock rewards
              </CardDescription>
            </div>
            <AustralianEmblem size="sm" className="opacity-70" />
          </div>

          <div className="grid grid-cols-2 gap-4 mt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{unlockedCount}/{userAchievements.length}</div>
              <div className="text-sm text-gray-400">Achievements Unlocked</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-racing-yellow">{totalPoints}</div>
              <div className="text-sm text-gray-400">Achievement Points</div>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid grid-cols-5 mb-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="driving">Driving</TabsTrigger>
              <TabsTrigger value="customization">Style</TabsTrigger>
              <TabsTrigger value="social">Social</TabsTrigger>
              <TabsTrigger value="special">Special</TabsTrigger>
            </TabsList>

            <motion.div
              className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {filteredAchievements.map((achievement, index) => (
                <motion.div
                  key={achievement.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex flex-col items-center"
                >
                  <EnhancedAchievementBadge
                    id={achievement.id}
                    name={achievement.name}
                    description={achievement.description}
                    category={achievement.category}
                    rarity={achievement.rarity}
                    unlocked={achievement.unlocked}
                    progress={achievement.requirements.current}
                    maxProgress={achievement.requirements.target}
                    unlockedAt={achievement.unlockedDate ? new Date(achievement.unlockedDate) : undefined}
                    reward={achievement.reward?.value}
                    size="lg"
                    showDetails={true}
                    animated={true}
                  />

                  {/* Achievement Details Below Badge */}
                  <div className="mt-3 text-center">
                    <h3 className={`font-semibold text-sm ${achievement.unlocked ? 'text-white' : 'text-gray-400'}`}>
                      {achievement.name}
                    </h3>
                    <div className="flex items-center justify-center gap-2 mt-1">
                      <Badge className={`${getRarityColor(achievement.rarity)} text-white text-xs`}>
                        {achievement.rarity}
                      </Badge>
                      <div className="flex items-center gap-1 text-xs text-racing-yellow">
                        <Trophy className="w-3 h-3" />
                        <span>{achievement.points}pts</span>
                      </div>
                    </div>

                    {!achievement.unlocked && achievement.requirements.current !== undefined && (
                      <div className="mt-2 w-full">
                        <div className="text-xs text-gray-400 mb-1">
                          {achievement.requirements.current}/{achievement.requirements.target}
                        </div>
                        <Progress
                          value={(achievement.requirements.current / achievement.requirements.target) * 100}
                          className="h-1"
                        />
                      </div>
                    )}

                    {achievement.reward && achievement.unlocked && (
                      <div className="mt-2 text-xs text-green-400">
                        🎁 {achievement.reward.value}
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

// Helper function to get current progress for achievements
function getCurrentProgress(achievement: Achievement, user: any): number {
  if (!user) return 0;

  switch (achievement.requirements.type) {
    case 'register':
      return 1;
    case 'total_score':
      return user.totalScore || 0;
    case 'founders_edition':
      return user.foundersEdition ? 1 : 0;
    case 'customizations':
      return user.garage?.[0]?.customization ?
        Object.values(user.garage[0].customization).filter(v => v !== null && v !== false).length : 0;
    default:
      return 0;
  }
}

export default AchievementSystem;
