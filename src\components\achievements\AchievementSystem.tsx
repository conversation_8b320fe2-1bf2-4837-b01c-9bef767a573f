import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Trophy, Star, Target, Zap, Wrench, Crown } from 'lucide-react';
import AustralianEmblem from '@/components/AustralianEmblem';
import { useAuth } from '@/context/AuthContext';

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: 'driving' | 'customization' | 'social' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  points: number;
  requirements: {
    type: string;
    target: number;
    current?: number;
  };
  unlocked: boolean;
  unlockedDate?: string;
  reward?: {
    type: 'car' | 'customization' | 'title' | 'badge';
    value: string;
  };
}

const ACHIEVEMENTS: Achievement[] = [
  {
    id: 'welcome',
    name: 'Welcome to the Family!',
    description: 'Register your account and join the Aussie Burnout Legends community',
    icon: <Crown className="w-6 h-6" />,
    category: 'special',
    rarity: 'common',
    points: 10,
    requirements: { type: 'register', target: 1 },
    unlocked: false
  },
  {
    id: 'first_burnout',
    name: "Smoke 'Em If You Got 'Em",
    description: 'Complete your first burnout challenge',
    icon: <Zap className="w-6 h-6" />,
    category: 'driving',
    rarity: 'common',
    points: 25,
    requirements: { type: 'burnout_score', target: 100 },
    unlocked: false
  },
  {
    id: 'first_drift',
    name: 'Sideways Sensation',
    description: 'Complete your first drift challenge',
    icon: <Target className="w-6 h-6" />,
    category: 'driving',
    rarity: 'common',
    points: 25,
    requirements: { type: 'drift_score', target: 100 },
    unlocked: false
  },
  {
    id: 'speed_demon',
    name: 'Speed Demon',
    description: 'Reach maximum speed in the driving simulator',
    icon: <Zap className="w-6 h-6" />,
    category: 'driving',
    rarity: 'rare',
    points: 50,
    requirements: { type: 'max_speed', target: 180 },
    unlocked: false
  },
  {
    id: 'customizer',
    name: 'Style Master',
    description: 'Customize your car with all available options',
    icon: <Wrench className="w-6 h-6" />,
    category: 'customization',
    rarity: 'rare',
    points: 75,
    requirements: { type: 'customizations', target: 10 },
    unlocked: false
  },
  {
    id: 'perfectionist',
    name: 'Perfectionist',
    description: 'Score over 5000 points in a single game session',
    icon: <Star className="w-6 h-6" />,
    category: 'driving',
    rarity: 'epic',
    points: 100,
    requirements: { type: 'single_session_score', target: 5000 },
    unlocked: false
  },
  {
    id: 'legend',
    name: 'Aussie Legend',
    description: 'Reach a total score of 50,000 points',
    icon: <Trophy className="w-6 h-6" />,
    category: 'driving',
    rarity: 'legendary',
    points: 500,
    requirements: { type: 'total_score', target: 50000 },
    unlocked: false,
    reward: {
      type: 'car',
      value: 'Holden Commodore VL Turbo'
    }
  },
  {
    id: 'founders_edition',
    name: 'Founding Father',
    description: 'One of the first 100 players to join Aussie Burnout Legends',
    icon: <Crown className="w-6 h-6" />,
    category: 'special',
    rarity: 'legendary',
    points: 1000,
    requirements: { type: 'founders_edition', target: 1 },
    unlocked: false,
    reward: {
      type: 'title',
      value: 'Founding Father'
    }
  }
];

interface AchievementSystemProps {
  className?: string;
}

const AchievementSystem: React.FC<AchievementSystemProps> = ({ className }) => {
  const { user } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Update achievements based on user data
  const userAchievements = ACHIEVEMENTS.map(achievement => ({
    ...achievement,
    unlocked: user?.achievements.includes(achievement.id) || false,
    unlockedDate: user?.achievements.includes(achievement.id) ? user.registrationDate : undefined,
    requirements: {
      ...achievement.requirements,
      current: getCurrentProgress(achievement, user)
    }
  }));

  const filteredAchievements = selectedCategory === 'all' 
    ? userAchievements 
    : userAchievements.filter(a => a.category === selectedCategory);

  const unlockedCount = userAchievements.filter(a => a.unlocked).length;
  const totalPoints = userAchievements
    .filter(a => a.unlocked)
    .reduce((sum, a) => sum + a.points, 0);

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-500';
      case 'rare': return 'bg-blue-500';
      case 'epic': return 'bg-purple-500';
      case 'legendary': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'driving': return <Zap className="w-4 h-4" />;
      case 'customization': return <Wrench className="w-4 h-4" />;
      case 'social': return <Star className="w-4 h-4" />;
      case 'special': return <Crown className="w-4 h-4" />;
      default: return <Trophy className="w-4 h-4" />;
    }
  };

  return (
    <div className={`${className}`}>
      <Card className="bg-black/40 border-racing-yellow/20">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl text-racing-yellow">Achievements</CardTitle>
              <CardDescription className="text-gray-400">
                Track your progress and unlock rewards
              </CardDescription>
            </div>
            <AustralianEmblem size="sm" className="opacity-70" />
          </div>
          
          <div className="grid grid-cols-2 gap-4 mt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{unlockedCount}/{userAchievements.length}</div>
              <div className="text-sm text-gray-400">Achievements Unlocked</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-racing-yellow">{totalPoints}</div>
              <div className="text-sm text-gray-400">Achievement Points</div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid grid-cols-5 mb-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="driving">Driving</TabsTrigger>
              <TabsTrigger value="customization">Style</TabsTrigger>
              <TabsTrigger value="social">Social</TabsTrigger>
              <TabsTrigger value="special">Special</TabsTrigger>
            </TabsList>
            
            <div className="space-y-4">
              {filteredAchievements.map((achievement) => (
                <Card 
                  key={achievement.id} 
                  className={`${achievement.unlocked 
                    ? 'bg-gradient-to-r from-racing-yellow/20 to-transparent border-racing-yellow/40' 
                    : 'bg-gray-900/40 border-gray-700'
                  }`}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4">
                      <div className={`p-3 rounded-full ${achievement.unlocked ? 'bg-racing-yellow text-black' : 'bg-gray-700 text-gray-400'}`}>
                        {achievement.icon}
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className={`font-semibold ${achievement.unlocked ? 'text-white' : 'text-gray-400'}`}>
                            {achievement.name}
                          </h3>
                          <Badge className={`${getRarityColor(achievement.rarity)} text-white text-xs`}>
                            {achievement.rarity}
                          </Badge>
                          <div className="flex items-center gap-1 text-xs text-gray-400">
                            {getCategoryIcon(achievement.category)}
                            <span>{achievement.category}</span>
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-400 mb-2">
                          {achievement.description}
                        </p>
                        
                        {!achievement.unlocked && achievement.requirements.current !== undefined && (
                          <div className="mb-2">
                            <div className="flex justify-between text-xs text-gray-400 mb-1">
                              <span>Progress</span>
                              <span>{achievement.requirements.current}/{achievement.requirements.target}</span>
                            </div>
                            <Progress 
                              value={(achievement.requirements.current / achievement.requirements.target) * 100} 
                              className="h-2"
                            />
                          </div>
                        )}
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Trophy className="w-4 h-4 text-racing-yellow" />
                            <span className="text-sm text-racing-yellow font-medium">
                              {achievement.points} points
                            </span>
                          </div>
                          
                          {achievement.unlocked && achievement.unlockedDate && (
                            <span className="text-xs text-gray-500">
                              Unlocked {new Date(achievement.unlockedDate).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                        
                        {achievement.reward && achievement.unlocked && (
                          <div className="mt-2 p-2 bg-racing-yellow/10 rounded border border-racing-yellow/20">
                            <div className="text-xs text-racing-yellow font-medium">
                              Reward: {achievement.reward.value}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

// Helper function to get current progress for achievements
function getCurrentProgress(achievement: Achievement, user: any): number {
  if (!user) return 0;
  
  switch (achievement.requirements.type) {
    case 'register':
      return 1;
    case 'total_score':
      return user.totalScore || 0;
    case 'founders_edition':
      return user.foundersEdition ? 1 : 0;
    case 'customizations':
      return user.garage?.[0]?.customization ? 
        Object.values(user.garage[0].customization).filter(v => v !== null && v !== false).length : 0;
    default:
      return 0;
  }
}

export default AchievementSystem;
