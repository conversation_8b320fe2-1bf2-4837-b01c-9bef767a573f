import React, { useEffect, useRef, useState } from 'react';
import { 
  VehicleState, 
  VehicleControls, 
  initVehicleState, 
  updateVehiclePhysics,
  calculateBurnoutScore,
  calculateDriftScore,
  VehicleConfig
} from './DrivingPhysics';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import AustralianEmblem from '@/components/AustralianEmblem';

interface DrivingGameProps {
  className?: string;
  vehicleConfig?: VehicleConfig;
  onScoreUpdate?: (score: number) => void;
}

const DrivingGame: React.FC<DrivingGameProps> = ({ 
  className,
  vehicleConfig,
  onScoreUpdate
}) => {
  const { toast } = useToast();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const requestRef = useRef<number>();
  const previousTimeRef = useRef<number>();
  const keysPressed = useRef<Set<string>>(new Set());
  
  // Game state
  const [isPlaying, setIsPlaying] = useState(false);
  const [score, setScore] = useState(0);
  const [gameMode, setGameMode] = useState<'burnout' | 'drift'>('burnout');
  const [vehicleState, setVehicleState] = useState<VehicleState>(initVehicleState(vehicleConfig));
  
  // Drift and burnout tracking
  const driftStartTime = useRef<number | null>(null);
  const burnoutStartTime = useRef<number | null>(null);
  const driftAngle = useRef(0);
  
  // Handle keyboard input
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysPressed.current.add(e.key.toLowerCase());
    };
    
    const handleKeyUp = (e: KeyboardEvent) => {
      keysPressed.current.delete(e.key.toLowerCase());
    };
    
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);
  
  // Main game loop
  const gameLoop = (time: number) => {
    if (previousTimeRef.current === undefined) {
      previousTimeRef.current = time;
    }
    
    const deltaTime = (time - previousTimeRef.current) / 1000; // Convert to seconds
    previousTimeRef.current = time;
    
    // Get current controls from keyboard input
    const controls: VehicleControls = {
      throttle: keysPressed.current.has('w') || keysPressed.current.has('arrowup') ? 1 : 0,
      brake: keysPressed.current.has('s') || keysPressed.current.has('arrowdown') ? 1 : 0,
      steering: (keysPressed.current.has('a') || keysPressed.current.has('arrowleft') ? -1 : 0) + 
                (keysPressed.current.has('d') || keysPressed.current.has('arrowright') ? 1 : 0),
      handbrake: keysPressed.current.has(' ') // Spacebar
    };
    
    // Update vehicle physics
    const newVehicleState = updateVehiclePhysics(vehicleState, controls, deltaTime);
    
    // Track drift and burnout for scoring
    if (newVehicleState.isDrifting && !vehicleState.isDrifting) {
      // Drift just started
      driftStartTime.current = time;
      driftAngle.current = newVehicleState.angularVelocity;
    } else if (!newVehicleState.isDrifting && vehicleState.isDrifting && driftStartTime.current) {
      // Drift just ended, calculate score
      const driftDuration = (time - driftStartTime.current) / 1000;
      const driftScore = calculateDriftScore(vehicleState, driftAngle.current, driftDuration);
      
      if (driftDuration > 0.5) { // Only count drifts longer than 0.5 seconds
        setScore(prevScore => {
          const newScore = prevScore + driftScore;
          if (onScoreUpdate) onScoreUpdate(newScore);
          return newScore;
        });
        
        toast({
          title: "Nice Drift!",
          description: `+${driftScore} points`,
          className: "bg-racing-black border-racing-yellow text-white",
        });
      }
      
      driftStartTime.current = null;
    }
    
    if (newVehicleState.isBurningOut && !vehicleState.isBurningOut) {
      // Burnout just started
      burnoutStartTime.current = time;
    } else if (!newVehicleState.isBurningOut && vehicleState.isBurningOut && burnoutStartTime.current) {
      // Burnout just ended, calculate score
      const burnoutDuration = (time - burnoutStartTime.current) / 1000;
      const burnoutScore = calculateBurnoutScore(vehicleState);
      
      if (burnoutDuration > 1) { // Only count burnouts longer than 1 second
        setScore(prevScore => {
          const newScore = prevScore + burnoutScore;
          if (onScoreUpdate) onScoreUpdate(newScore);
          return newScore;
        });
        
        toast({
          title: "Sick Burnout!",
          description: `+${burnoutScore} points`,
          className: "bg-racing-black border-racing-yellow text-white",
        });
      }
      
      burnoutStartTime.current = null;
    }
    
    // Update state
    setVehicleState(newVehicleState);
    
    // Draw game
    drawGame(newVehicleState);
    
    // Continue game loop
    if (isPlaying) {
      requestRef.current = requestAnimationFrame(gameLoop);
    }
  };
  
  // Start/stop game loop when isPlaying changes
  useEffect(() => {
    if (isPlaying) {
      requestRef.current = requestAnimationFrame(gameLoop);
      toast({
        title: "Game Started!",
        description: gameMode === 'burnout' 
          ? "Hold throttle and handbrake to perform burnouts!" 
          : "Use handbrake and steering to drift!",
        className: "bg-racing-black border-racing-yellow text-white",
      });
    } else {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    }
    
    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [isPlaying, gameMode]);
  
  // Reset game state
  const resetGame = () => {
    setVehicleState(initVehicleState(vehicleConfig));
    setScore(0);
    if (onScoreUpdate) onScoreUpdate(0);
    driftStartTime.current = null;
    burnoutStartTime.current = null;
  };
  
  // Draw the game on canvas
  const drawGame = (state: VehicleState) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Set canvas dimensions to match display size
    if (canvas.width !== canvas.clientWidth || canvas.height !== canvas.clientHeight) {
      canvas.width = canvas.clientWidth;
      canvas.height = canvas.clientHeight;
    }
    
    // Draw track (simple oval)
    ctx.save();
    ctx.strokeStyle = '#444';
    ctx.lineWidth = 20;
    ctx.beginPath();
    ctx.ellipse(canvas.width / 2, canvas.height / 2, canvas.width / 3, canvas.height / 3, 0, 0, Math.PI * 2);
    ctx.stroke();
    
    // Draw track center line
    ctx.strokeStyle = '#FFF';
    ctx.lineWidth = 2;
    ctx.setLineDash([20, 10]);
    ctx.beginPath();
    ctx.ellipse(canvas.width / 2, canvas.height / 2, canvas.width / 3, canvas.height / 3, 0, 0, Math.PI * 2);
    ctx.stroke();
    ctx.setLineDash([]);
    ctx.restore();
    
    // Transform to vehicle position
    ctx.save();
    
    // Center the view on the canvas
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    
    // Calculate vehicle position on screen
    const screenX = centerX + state.positionX * 10; // Scale factor for visibility
    const screenY = centerY - state.positionY * 10; // Y is inverted in canvas
    
    // Draw tire smoke if burning out or drifting
    if (state.rearTireSmoke > 10) {
      const smokeOpacity = state.rearTireSmoke / 100;
      const smokeSize = state.rearTireSmoke / 5;
      
      ctx.save();
      ctx.globalAlpha = smokeOpacity * 0.7;
      ctx.fillStyle = '#FFFFFF';
      
      // Draw smoke behind the car
      ctx.translate(screenX, screenY);
      ctx.rotate(state.rotation);
      
      // Left rear tire smoke
      ctx.beginPath();
      ctx.arc(-15, 10, smokeSize, 0, Math.PI * 2);
      ctx.fill();
      
      // Right rear tire smoke
      ctx.beginPath();
      ctx.arc(-15, -10, smokeSize, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.restore();
    }
    
    // Draw vehicle
    ctx.save();
    ctx.translate(screenX, screenY);
    ctx.rotate(state.rotation);
    
    // Car body
    ctx.fillStyle = '#FF0000'; // Red car
    ctx.fillRect(-20, -10, 40, 20);
    
    // Windshield
    ctx.fillStyle = '#ADD8E6';
    ctx.fillRect(-5, -8, 15, 16);
    
    // Wheels
    ctx.fillStyle = '#333';
    ctx.fillRect(-15, -15, 8, 5); // Left front
    ctx.fillRect(-15, 10, 8, 5);  // Right front
    ctx.fillRect(10, -15, 8, 5);  // Left rear
    ctx.fillRect(10, 10, 8, 5);   // Right rear
    
    ctx.restore();
    
    // Draw HUD
    drawHUD(ctx, state);
  };
  
  // Draw heads-up display
  const drawHUD = (ctx: CanvasRenderingContext2D, state: VehicleState) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    ctx.save();
    
    // Speed display
    ctx.fillStyle = '#FFF';
    ctx.font = '16px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(`Speed: ${Math.round(state.speed)} km/h`, 20, 30);
    
    // RPM display
    ctx.fillText(`RPM: ${Math.round(state.currentRPM)}`, 20, 50);
    ctx.fillText(`Gear: ${state.gear}`, 20, 70);
    
    // Score
    ctx.textAlign = 'right';
    ctx.font = '24px Arial';
    ctx.fillText(`Score: ${score}`, canvas.width - 20, 30);
    
    // Game mode
    ctx.font = '16px Arial';
    ctx.fillText(`Mode: ${gameMode === 'burnout' ? 'Burnout Challenge' : 'Drift Challenge'}`, canvas.width - 20, 50);
    
    // Status indicators
    if (state.isBurningOut) {
      ctx.fillStyle = '#FF0000';
      ctx.fillText('BURNOUT!', canvas.width - 20, 80);
    } else if (state.isDrifting) {
      ctx.fillStyle = '#00BFFF';
      ctx.fillText('DRIFTING!', canvas.width - 20, 80);
    }
    
    // Tire health bar
    const barWidth = 150;
    const barHeight = 15;
    const barX = canvas.width - barWidth - 20;
    const barY = canvas.height - 30;
    
    ctx.fillStyle = '#333';
    ctx.fillRect(barX, barY, barWidth, barHeight);
    
    const healthWidth = (state.rearTireHealth / 100) * barWidth;
    ctx.fillStyle = state.rearTireHealth > 50 ? '#00FF00' : state.rearTireHealth > 25 ? '#FFFF00' : '#FF0000';
    ctx.fillRect(barX, barY, healthWidth, barHeight);
    
    ctx.fillStyle = '#FFF';
    ctx.textAlign = 'center';
    ctx.fillText('Tire Health', barX + barWidth / 2, barY - 5);
    
    // Controls help
    ctx.textAlign = 'left';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.font = '12px Arial';
    ctx.fillText('Controls: WASD or Arrow Keys to drive, SPACE for handbrake', 20, canvas.height - 10);
    
    ctx.restore();
  };
  
  return (
    <div className={`${className} flex flex-col gap-4`}>
      <Card className="bg-black/40 border-racing-yellow/20">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-2xl text-racing-yellow">Aussie Burnout Simulator</CardTitle>
            <AustralianEmblem size="sm" className="opacity-70" />
          </div>
          <CardDescription className="text-gray-400">
            Show off your skills with burnouts and drifts!
          </CardDescription>
        </CardHeader>
        
        <CardContent className="pb-2">
          <div className="relative w-full aspect-video bg-gray-900 rounded-md overflow-hidden">
            <canvas 
              ref={canvasRef} 
              className="w-full h-full"
              tabIndex={0} // Make canvas focusable for keyboard input
            />
            
            {!isPlaying && (
              <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/70">
                <h3 className="text-2xl font-bold text-white mb-4">Ready to Burn Some Rubber?</h3>
                <div className="flex gap-4">
                  <Button 
                    className="bg-racing-red hover:bg-red-700 text-white"
                    onClick={() => {
                      setGameMode('burnout');
                      resetGame();
                      setIsPlaying(true);
                    }}
                  >
                    Burnout Challenge
                  </Button>
                  <Button 
                    className="bg-[#00008B] hover:bg-blue-800 text-white"
                    onClick={() => {
                      setGameMode('drift');
                      resetGame();
                      setIsPlaying(true);
                    }}
                  >
                    Drift Challenge
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-between">
          {isPlaying ? (
            <Button 
              variant="destructive"
              onClick={() => setIsPlaying(false)}
            >
              End Game
            </Button>
          ) : (
            <Button 
              variant="outline"
              onClick={resetGame}
            >
              Reset Score
            </Button>
          )}
          
          <div className="text-white text-xl font-bold">
            Score: {score}
          </div>
        </CardFooter>
      </Card>
      
      <div className="text-sm text-gray-400 text-center">
        <p>Perform burnouts by holding throttle (W/Up) and handbrake (Space) while stationary.</p>
        <p>Drift by using handbrake during turns at high speed.</p>
      </div>
    </div>
  );
};

export default DrivingGame;
