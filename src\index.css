

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 7%;
    --foreground: 0 0% 95%;

    --card: 0 0% 10%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 95%;

    --primary: 240 100% 27%;  /* Australian flag blue */
    --primary-foreground: 0 0% 100%;  /* White for text on blue */

    --secondary: 0 100% 50%;  /* Red */
    --secondary-foreground: 0 0% 100%;  /* White for text on red */

    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 70%;

    --accent: 240 100% 27%;  /* Australian flag blue */
    --accent-foreground: 0 0% 95%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 95%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 240 100% 27%;  /* Australian flag blue */

    --radius: 0.5rem;

    --sidebar-background: 240 100% 10%;  /* Darker blue for sidebar */
    --sidebar-foreground: 0 0% 90%;
    --sidebar-primary: 0 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 100% 15%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 240 100% 20%;
    --sidebar-ring: 0 100% 50%;  /* Red */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .racing-stripe {
    @apply relative;
  }
  
  .racing-stripe::before {
    content: '';
    @apply absolute top-0 bottom-0 left-3 w-8 bg-australia-red opacity-90 -skew-x-12;
  }
  
  .racing-stripe::after {
    content: '';
    @apply absolute top-0 bottom-0 left-12 w-4 bg-white opacity-90 -skew-x-12;
  }
  
  .australia-stripe {
    @apply relative overflow-hidden;
  }
  
  .australia-stripe::before {
    content: '';
    @apply absolute inset-0 bg-aus-flag-gradient opacity-80;
  }
  
  .tire-track {
    @apply relative overflow-hidden;
  }
  
  .tire-track::before {
    content: '';
    @apply absolute w-full h-12 bg-black bg-opacity-10;
    mask-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }
  
  .founders-badge {
    @apply bg-aus-flag-gradient text-white font-bold py-1 px-3 rounded-full text-sm uppercase tracking-wide shadow-lg transform rotate-[-5deg] absolute top-4 right-4;
  }
  
  .southern-cross {
    @apply relative;
  }
  
  .southern-cross::before {
    content: '';
    @apply absolute -top-1 -right-1 w-6 h-6 bg-white opacity-90 rotate-45;
  }
  
  .checkerboard-flag {
    background-size: 20px 20px;
    background-image: 
      linear-gradient(45deg, #000 25%, transparent 25%, transparent 75%, #000 75%, #000), 
      linear-gradient(45deg, #000 25%, transparent 25%, transparent 75%, #000 75%, #000);
    background-position: 0 0, 10px 10px;
    @apply bg-white;
  }
  
  .smoke-animation {
    @apply before:content-[''] before:absolute before:bottom-0 before:left-1/2 before:-translate-x-1/2 before:w-32 before:h-32 before:rounded-full before:bg-white before:opacity-0 before:animate-burnout-smoke;
  }

  .nav-link {
    @apply relative text-foreground hover:text-australia-red transition-colors duration-300 px-4 py-2;
  }
  
  .nav-link::after {
    content: '';
    @apply absolute left-0 bottom-0 w-0 h-0.5 bg-australia-red transition-all duration-300;
  }
  
  .nav-link:hover::after {
    @apply w-full;
  }
  
  .btn-aussie {
    @apply bg-australia-red hover:bg-red-700 text-white font-bold py-3 px-6 rounded shadow-lg transform transition duration-300 hover:scale-105 hover:shadow-xl uppercase tracking-wider;
  }
  
  .aussie-star {
    @apply absolute bg-white transform rotate-45 animate-star-twinkle;
  }
}
