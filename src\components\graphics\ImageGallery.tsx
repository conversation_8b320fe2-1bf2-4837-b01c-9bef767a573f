import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ChevronLeft, ChevronRight, Download, Share2, ZoomIn, ZoomOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CarImage, EventImage } from '@/components/graphics/EnhancedImageLoader';
import { useGraphics } from '@/components/graphics/GraphicsOptimizer';

interface ImageGalleryProps {
  images: Array<{
    id: string;
    src: string;
    alt: string;
    title?: string;
    description?: string;
    type?: 'car' | 'event' | 'background';
    category?: string;
  }>;
  isOpen: boolean;
  onClose: () => void;
  initialIndex?: number;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  isOpen,
  onClose,
  initialIndex = 0
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const { getOptimizedImageUrl, shouldRenderEffect } = useGraphics();

  const currentImage = images[currentIndex];

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length);
    setIsZoomed(false);
    setZoomLevel(1);
  };

  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
    setIsZoomed(false);
    setZoomLevel(1);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowRight') nextImage();
    if (e.key === 'ArrowLeft') prevImage();
    if (e.key === 'Escape') onClose();
  };

  const handleDownload = async () => {
    try {
      const response = await fetch(currentImage.src);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${currentImage.title || currentImage.alt}.jpg`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: currentImage.title || currentImage.alt,
          text: currentImage.description || 'Check out this awesome car!',
          url: window.location.href
        });
      } catch (error) {
        console.error('Share failed:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const zoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.5, 3));
    setIsZoomed(true);
  };

  const zoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.5, 1));
    if (zoomLevel <= 1.5) setIsZoomed(false);
  };

  if (!isOpen || !currentImage) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        {/* Close Button */}
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-4 right-4 z-10 text-white hover:bg-white/20"
          onClick={onClose}
        >
          <X className="w-6 h-6" />
        </Button>

        {/* Navigation Buttons */}
        {images.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="sm"
              className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 text-white hover:bg-white/20"
              onClick={(e) => {
                e.stopPropagation();
                prevImage();
              }}
            >
              <ChevronLeft className="w-8 h-8" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 text-white hover:bg-white/20"
              onClick={(e) => {
                e.stopPropagation();
                nextImage();
              }}
            >
              <ChevronRight className="w-8 h-8" />
            </Button>
          </>
        )}

        {/* Action Buttons */}
        <div className="absolute top-4 left-4 z-10 flex gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-white/20"
            onClick={(e) => {
              e.stopPropagation();
              zoomIn();
            }}
          >
            <ZoomIn className="w-5 h-5" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-white/20"
            onClick={(e) => {
              e.stopPropagation();
              zoomOut();
            }}
          >
            <ZoomOut className="w-5 h-5" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-white/20"
            onClick={(e) => {
              e.stopPropagation();
              handleDownload();
            }}
          >
            <Download className="w-5 h-5" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="text-white hover:bg-white/20"
            onClick={(e) => {
              e.stopPropagation();
              handleShare();
            }}
          >
            <Share2 className="w-5 h-5" />
          </Button>
        </div>

        {/* Main Image */}
        <div className="flex items-center justify-center h-full p-8">
          <motion.div
            className={`relative max-w-full max-h-full ${isZoomed ? 'cursor-move' : 'cursor-zoom-in'}`}
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ 
              scale: shouldRenderEffect('animations') ? 1 : 1, 
              opacity: 1 
            }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ duration: 0.3 }}
            style={{ transform: `scale(${zoomLevel})` }}
            onClick={(e) => e.stopPropagation()}
          >
            {currentImage.type === 'car' ? (
              <CarImage
                src={getOptimizedImageUrl(currentImage.src, 1200, 800)}
                alt={currentImage.alt}
                carModel={currentImage.category}
                width={1200}
                height={800}
                quality="ultra"
                className="max-w-full max-h-full object-contain"
              />
            ) : currentImage.type === 'event' ? (
              <EventImage
                src={getOptimizedImageUrl(currentImage.src, 1200, 800)}
                alt={currentImage.alt}
                eventType={currentImage.category}
                width={1200}
                height={800}
                quality="ultra"
                className="max-w-full max-h-full object-contain"
              />
            ) : (
              <img
                src={getOptimizedImageUrl(currentImage.src, 1200, 800)}
                alt={currentImage.alt}
                className="max-w-full max-h-full object-contain"
              />
            )}
          </motion.div>
        </div>

        {/* Image Info */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-white text-2xl font-bold mb-2">
              {currentImage.title || currentImage.alt}
            </h2>
            {currentImage.description && (
              <p className="text-gray-300 mb-4">{currentImage.description}</p>
            )}
            
            {/* Image Counter */}
            {images.length > 1 && (
              <div className="flex justify-between items-center">
                <span className="text-gray-400 text-sm">
                  {currentIndex + 1} of {images.length}
                </span>
                
                {/* Thumbnail Navigation */}
                <div className="flex gap-2 max-w-md overflow-x-auto">
                  {images.map((image, index) => (
                    <button
                      key={image.id}
                      className={`flex-shrink-0 w-16 h-12 rounded overflow-hidden border-2 transition-all ${
                        index === currentIndex 
                          ? 'border-racing-yellow' 
                          : 'border-transparent hover:border-white/50'
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        setCurrentIndex(index);
                        setIsZoomed(false);
                        setZoomLevel(1);
                      }}
                    >
                      <img
                        src={getOptimizedImageUrl(image.src, 64, 48)}
                        alt={image.alt}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Loading Indicator */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none">
          <motion.div
            className="w-8 h-8 border-4 border-racing-yellow border-t-transparent rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

// Gallery Grid Component
interface ImageGridProps {
  images: Array<{
    id: string;
    src: string;
    alt: string;
    title?: string;
    description?: string;
    type?: 'car' | 'event' | 'background';
    category?: string;
  }>;
  columns?: number;
  onImageClick?: (index: number) => void;
}

export const ImageGrid: React.FC<ImageGridProps> = ({
  images,
  columns = 3,
  onImageClick
}) => {
  const { getOptimizedImageUrl, shouldRenderEffect } = useGraphics();

  return (
    <div 
      className={`grid gap-4`}
      style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
    >
      {images.map((image, index) => (
        <motion.div
          key={image.id}
          className="relative aspect-video rounded-lg overflow-hidden cursor-pointer group"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          whileHover={shouldRenderEffect('animations') ? { scale: 1.05 } : {}}
          onClick={() => onImageClick?.(index)}
        >
          {image.type === 'car' ? (
            <CarImage
              src={getOptimizedImageUrl(image.src, 400, 300)}
              alt={image.alt}
              carModel={image.category}
              width={400}
              height={300}
              className="w-full h-full object-cover"
            />
          ) : image.type === 'event' ? (
            <EventImage
              src={getOptimizedImageUrl(image.src, 400, 300)}
              alt={image.alt}
              eventType={image.category}
              width={400}
              height={300}
              className="w-full h-full object-cover"
            />
          ) : (
            <img
              src={getOptimizedImageUrl(image.src, 400, 300)}
              alt={image.alt}
              className="w-full h-full object-cover"
            />
          )}
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all duration-300 flex items-center justify-center">
            <ZoomIn className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </div>
          
          {/* Title */}
          {image.title && (
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3">
              <h3 className="text-white font-semibold text-sm">{image.title}</h3>
            </div>
          )}
        </motion.div>
      ))}
    </div>
  );
};

export default ImageGallery;
