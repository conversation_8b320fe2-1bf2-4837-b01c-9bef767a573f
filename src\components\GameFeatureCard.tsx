
import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface GameFeatureCardProps {
  title: string;
  description: string;
  status: 'planned' | 'in-progress' | 'completed';
  icon: React.ReactNode;
  tags?: string[];
  className?: string;
}

const GameFeatureCard: React.FC<GameFeatureCardProps> = ({
  title,
  description,
  status,
  icon,
  tags = [],
  className,
}) => {
  const statusColors = {
    'planned': 'bg-amber-500',
    'in-progress': 'bg-blue-500',
    'completed': 'bg-green-500'
  };

  const statusLabels = {
    'planned': 'Planned',
    'in-progress': 'In Progress',
    'completed': 'Completed'
  };

  return (
    <Card className={`overflow-hidden transition-all hover:shadow-lg ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg font-bold">{title}</CardTitle>
          <div className={`px-2 py-1 rounded-full text-xs font-medium text-white ${statusColors[status]}`}>
            {statusLabels[status]}
          </div>
        </div>
        <CardDescription className="text-sm text-gray-500">{description}</CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="text-4xl mb-4 text-racing-red">{icon}</div>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
      </CardContent>
      <div className="h-1 bg-gradient-to-r from-racing-red to-racing-yellow"></div>
    </Card>
  );
};

export default GameFeatureCard;
