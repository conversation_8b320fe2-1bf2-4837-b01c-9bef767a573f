import { Post } from "../types/post";

interface PostCardProps {
  post: Post;
}

export default function PostCard({ post }: PostCardProps) {
  return (
    <article className="card p-6 mb-6 flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <img
          src={post.author.avatar}
          alt={post.author.name}
          className="w-12 h-12 rounded-full object-cover border-2 border-primary"
        />
        <div>
          <div className="font-semibold text-lg">{post.author.name}</div>
          <div className="text-gray-500 text-sm">{post.author.handle}</div>
        </div>
        <span className="ml-auto text-xs text-gray-400">
          {new Date(post.createdAt).toLocaleDateString(undefined, {
            month: "short",
            day: "numeric"
          })}
        </span>
      </div>
      <div className="text-base text-gray-800">{post.content}</div>
      {post.image && (
        <img
          src={post.image}
          alt="Post visual"
          className="rounded-xl w-full max-h-72 object-cover border border-gray-100"
        />
      )}
    </article>
  );
}
