import React, { useState } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import DrivingGame from '@/components/game/DrivingGame';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import AustralianEmblem from '@/components/AustralianEmblem';

const PlayGame = () => {
  const [highScore, setHighScore] = useState(0);
  
  const handleScoreUpdate = (newScore: number) => {
    if (newScore > highScore) {
      setHighScore(newScore);
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-[#000424] to-[#001440]">
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white">Aussie Burnout Legends</h1>
          <p className="text-gray-400 mt-2">Experience the thrill of Australian car culture</p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <DrivingGame onScoreUpdate={handleScoreUpdate} />
          </div>
          
          <div className="space-y-6">
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-xl text-racing-yellow">Leaderboard</CardTitle>
                  <AustralianEmblem size="sm" className="opacity-70" />
                </div>
                <CardDescription className="text-gray-400">
                  Top burnout and drift scores
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <Tabs defaultValue="local">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="local">Your Scores</TabsTrigger>
                    <TabsTrigger value="global">Global</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="local" className="space-y-4 pt-4">
                    <div className="flex justify-between items-center border-b border-gray-800 pb-2">
                      <span className="text-white">Your High Score</span>
                      <span className="text-racing-yellow font-bold">{highScore}</span>
                    </div>
                    
                    <div className="text-center text-sm text-gray-500 italic">
                      {highScore > 0 
                        ? "Keep playing to beat your high score!" 
                        : "Play a game to record your score!"}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="global" className="pt-4">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center border-b border-gray-800 pb-2">
                        <div className="flex items-center gap-2">
                          <span className="text-racing-yellow font-bold">1.</span>
                          <span className="text-white">BurnoutKing</span>
                        </div>
                        <span className="text-racing-yellow font-bold">12,450</span>
                      </div>
                      
                      <div className="flex justify-between items-center border-b border-gray-800 pb-2">
                        <div className="flex items-center gap-2">
                          <span className="text-racing-yellow font-bold">2.</span>
                          <span className="text-white">DriftMaster</span>
                        </div>
                        <span className="text-racing-yellow font-bold">10,820</span>
                      </div>
                      
                      <div className="flex justify-between items-center border-b border-gray-800 pb-2">
                        <div className="flex items-center gap-2">
                          <span className="text-racing-yellow font-bold">3.</span>
                          <span className="text-white">AussieTire</span>
                        </div>
                        <span className="text-racing-yellow font-bold">9,675</span>
                      </div>
                      
                      <div className="text-center text-sm text-gray-500 italic mt-4">
                        Login to compete on the global leaderboard!
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
            
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <CardTitle className="text-xl text-racing-yellow">Game Tips</CardTitle>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-white font-medium mb-1">Perfect Burnouts</h3>
                  <p className="text-sm text-gray-400">
                    Hold the throttle and handbrake simultaneously while stationary. 
                    Release before your tires are completely destroyed for maximum points!
                  </p>
                </div>
                
                <div>
                  <h3 className="text-white font-medium mb-1">Drifting Technique</h3>
                  <p className="text-sm text-gray-400">
                    Approach corners at high speed, then tap the handbrake while turning. 
                    Maintain throttle control to extend your drift for higher scores.
                  </p>
                </div>
                
                <div>
                  <h3 className="text-white font-medium mb-1">Scoring System</h3>
                  <p className="text-sm text-gray-400">
                    Burnout scores depend on smoke generated and tire management.
                    Drift scores are based on angle, speed, and duration of the drift.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default PlayGame;
