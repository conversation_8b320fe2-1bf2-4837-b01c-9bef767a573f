
import React from 'react';

interface AustralianEmblemProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animate?: boolean;
}

const AustralianEmblem: React.FC<AustralianEmblemProps> = ({ 
  className = '', 
  size = 'md',
  animate = false
}) => {
  // Size mapping
  const sizeMap = {
    sm: 'w-6 h-4',
    md: 'w-12 h-8',
    lg: 'w-20 h-12',
    xl: 'w-32 h-20'
  };
  
  const sizeClass = sizeMap[size];
  
  return (
    <div className={`relative ${sizeClass} ${className} ${animate ? 'flag-wave' : ''}`}>
      {/* Main blue background */}
      <div className="absolute inset-0 bg-[#00008B]"></div>
      
      {/* Union Jack in the top left quarter */}
      <div className="absolute top-0 left-0 w-1/2 h-1/2 bg-[#00008B]">
        {/* White background cross */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full h-[20%] bg-white"></div>
          <div className="absolute h-full w-[20%] bg-white"></div>
        </div>
        
        {/* Red foreground cross */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-full h-[10%] bg-[#FF0000]"></div>
          <div className="absolute h-full w-[10%] bg-[#FF0000]"></div>
        </div>
        
        {/* White diagonals */}
        <div className="absolute inset-0">
          <div className="absolute w-[100%] h-[15%] bg-white origin-top-left rotate-45 translate-x-[-5%] translate-y-[50%]"></div>
          <div className="absolute w-[100%] h-[15%] bg-white origin-bottom-left -rotate-45 translate-x-[-5%] translate-y-[-50%]"></div>
        </div>
        
        {/* Red diagonals overlay */}
        <div className="absolute inset-0">
          <div className="absolute w-[100%] h-[8%] bg-[#FF0000] origin-top-left rotate-45 translate-x-[-5%] translate-y-[75%]"></div>
          <div className="absolute w-[100%] h-[8%] bg-[#FF0000] origin-bottom-left -rotate-45 translate-x-[-5%] translate-y-[-75%]"></div>
        </div>
      </div>
      
      {/* Commonwealth Star - large 7-pointed star below the Union Jack */}
      <div className="absolute top-[30%] left-[25%] w-[12%] h-[12%]">
        <div className="absolute w-full h-full bg-white transform rotate-0"></div>
        <div className="absolute w-full h-full bg-white transform rotate-[25.7deg]"></div>
        <div className="absolute w-full h-full bg-white transform rotate-[51.4deg]"></div>
      </div>
      
      {/* Southern Cross constellation - 5 stars of various sizes */}
      {/* Alpha Crucis - the bottom star */}
      <div className="absolute bottom-[15%] right-[30%] w-[8%] h-[8%]">
        <div className="absolute w-full h-full bg-white transform rotate-0"></div>
        <div className="absolute w-full h-full bg-white transform rotate-45"></div>
      </div>
      
      {/* Beta Crucis - upper right */}
      <div className="absolute top-[30%] right-[25%] w-[8%] h-[8%]">
        <div className="absolute w-full h-full bg-white transform rotate-0"></div>
        <div className="absolute w-full h-full bg-white transform rotate-45"></div>
      </div>
      
      {/* Gamma Crucis - top star */}
      <div className="absolute top-[15%] right-[40%] w-[8%] h-[8%]">
        <div className="absolute w-full h-full bg-white transform rotate-0"></div>
        <div className="absolute w-full h-full bg-white transform rotate-45"></div>
      </div>
      
      {/* Delta Crucis - left star */}
      <div className="absolute top-[40%] right-[45%] w-[6%] h-[6%]">
        <div className="absolute w-full h-full bg-white transform rotate-0"></div>
        <div className="absolute w-full h-full bg-white transform rotate-45"></div>
      </div>
      
      {/* Epsilon Crucis - smaller central star */}
      <div className="absolute top-[45%] right-[35%] w-[4%] h-[4%]">
        <div className="absolute w-full h-full bg-white transform rotate-0"></div>
        <div className="absolute w-full h-full bg-white transform rotate-45"></div>
      </div>
    </div>
  );
};

export default AustralianEmblem;
