import React from 'react';
import { motion } from 'framer-motion';

interface EnhancedBackgroundProps {
  variant: 'hero' | 'garage' | 'track' | 'showroom' | 'outback' | 'city' | 'sunset';
  children?: React.ReactNode;
  className?: string;
  animated?: boolean;
}

const EnhancedBackground: React.FC<EnhancedBackgroundProps> = ({
  variant,
  children,
  className,
  animated = true
}) => {
  const getBackgroundStyles = () => {
    switch (variant) {
      case 'hero':
        return {
          background: `
            linear-gradient(135deg, 
              rgba(0, 4, 36, 0.95) 0%, 
              rgba(0, 20, 64, 0.9) 50%, 
              rgba(139, 0, 0, 0.8) 100%
            ),
            radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 69, 0, 0.1) 0%, transparent 50%)
          `,
          backgroundSize: '100% 100%, 800px 800px, 600px 600px',
          backgroundPosition: 'center, left bottom, right top'
        };
      
      case 'garage':
        return {
          background: `
            linear-gradient(180deg, 
              rgba(28, 28, 28, 0.95) 0%, 
              rgba(10, 10, 10, 0.98) 100%
            ),
            repeating-linear-gradient(
              90deg,
              transparent,
              transparent 98px,
              rgba(255, 215, 0, 0.03) 100px
            )
          `,
          backgroundSize: '100% 100%, 100px 100%'
        };
      
      case 'track':
        return {
          background: `
            linear-gradient(45deg, 
              rgba(47, 79, 79, 0.9) 0%, 
              rgba(25, 25, 112, 0.8) 50%, 
              rgba(0, 0, 0, 0.95) 100%
            ),
            repeating-linear-gradient(
              45deg,
              transparent,
              transparent 10px,
              rgba(255, 255, 255, 0.02) 10px,
              rgba(255, 255, 255, 0.02) 20px
            )
          `
        };
      
      case 'showroom':
        return {
          background: `
            linear-gradient(135deg, 
              rgba(240, 248, 255, 0.05) 0%, 
              rgba(25, 25, 112, 0.8) 50%, 
              rgba(0, 0, 0, 0.95) 100%
            ),
            radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 70%)
          `
        };
      
      case 'outback':
        return {
          background: `
            linear-gradient(180deg, 
              rgba(255, 140, 0, 0.3) 0%, 
              rgba(139, 69, 19, 0.6) 30%, 
              rgba(160, 82, 45, 0.8) 70%, 
              rgba(0, 0, 0, 0.95) 100%
            ),
            radial-gradient(ellipse at top, rgba(255, 215, 0, 0.1) 0%, transparent 50%)
          `
        };
      
      case 'city':
        return {
          background: `
            linear-gradient(180deg, 
              rgba(25, 25, 112, 0.8) 0%, 
              rgba(72, 61, 139, 0.6) 30%, 
              rgba(0, 0, 0, 0.9) 70%, 
              rgba(0, 0, 0, 0.98) 100%
            ),
            repeating-linear-gradient(
              90deg,
              transparent,
              transparent 50px,
              rgba(255, 215, 0, 0.02) 52px
            )
          `
        };
      
      case 'sunset':
        return {
          background: `
            linear-gradient(180deg, 
              rgba(255, 94, 77, 0.4) 0%, 
              rgba(255, 154, 0, 0.5) 25%, 
              rgba(255, 206, 84, 0.3) 50%, 
              rgba(139, 0, 139, 0.6) 75%, 
              rgba(0, 0, 0, 0.95) 100%
            )
          `
        };
      
      default:
        return {
          background: 'linear-gradient(135deg, rgba(0, 4, 36, 0.95) 0%, rgba(0, 20, 64, 0.9) 100%)'
        };
    }
  };

  const backgroundStyles = getBackgroundStyles();

  return (
    <div 
      className={`relative min-h-screen ${className}`}
      style={backgroundStyles}
    >
      {/* Animated particles for certain variants */}
      {animated && (variant === 'hero' || variant === 'showroom') && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {Array.from({ length: 20 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-racing-yellow/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.2, 0.8, 0.2],
                scale: [1, 1.5, 1]
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>
      )}

      {/* Garage-specific elements */}
      {variant === 'garage' && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Tool shadows */}
          <div className="absolute top-20 left-10 w-8 h-32 bg-black/20 rounded-full blur-sm transform rotate-12" />
          <div className="absolute top-32 right-16 w-6 h-24 bg-black/20 rounded-full blur-sm transform -rotate-6" />
          
          {/* Workbench silhouette */}
          <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-black/30 to-transparent" />
        </div>
      )}

      {/* Track-specific elements */}
      {variant === 'track' && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Racing lines */}
          <div className="absolute top-1/3 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/10 to-transparent" />
          <div className="absolute top-2/3 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/10 to-transparent" />
          
          {/* Tire marks */}
          <div className="absolute bottom-20 left-1/4 w-64 h-2 bg-black/30 rounded-full blur-sm transform -rotate-12" />
          <div className="absolute bottom-32 right-1/3 w-48 h-2 bg-black/30 rounded-full blur-sm transform rotate-6" />
        </div>
      )}

      {/* Outback-specific elements */}
      {variant === 'outback' && animated && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Heat shimmer effect */}
          <motion.div
            className="absolute bottom-0 left-0 w-full h-64 bg-gradient-to-t from-transparent via-white/5 to-transparent"
            animate={{
              scaleY: [1, 1.1, 1],
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          
          {/* Dust particles */}
          {Array.from({ length: 10 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-orange-200/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                bottom: `${Math.random() * 30}%`,
              }}
              animate={{
                x: [0, 50, 0],
                y: [0, -30, 0],
                opacity: [0, 0.6, 0]
              }}
              transition={{
                duration: 6 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 3
              }}
            />
          ))}
        </div>
      )}

      {/* City-specific elements */}
      {variant === 'city' && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Building silhouettes */}
          <div className="absolute bottom-0 left-0 w-full h-48 bg-gradient-to-t from-black/40 to-transparent" />
          
          {/* City lights */}
          {Array.from({ length: 15 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-yellow-400/60 rounded-full"
              style={{
                left: `${10 + (i * 5)}%`,
                bottom: `${20 + Math.random() * 30}%`,
              }}
              animate={{
                opacity: [0.3, 1, 0.3]
              }}
              transition={{
                duration: 2 + Math.random(),
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>
      )}

      {/* Sunset-specific elements */}
      {variant === 'sunset' && animated && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Sun rays */}
          <motion.div
            className="absolute top-10 right-20 w-64 h-64 bg-gradient-radial from-yellow-400/20 via-orange-400/10 to-transparent rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 0.8, 0.5]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          
          {/* Clouds */}
          <motion.div
            className="absolute top-32 left-1/4 w-32 h-8 bg-white/10 rounded-full blur-sm"
            animate={{
              x: [0, 50, 0]
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        </div>
      )}

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>

      {/* Overlay texture */}
      <div 
        className="absolute inset-0 opacity-5 pointer-events-none"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}
      />
    </div>
  );
};

// Specific background components for easy use
export const HeroBackground: React.FC<{ children?: React.ReactNode; className?: string }> = ({ children, className }) => (
  <EnhancedBackground variant="hero" className={className}>{children}</EnhancedBackground>
);

export const GarageBackground: React.FC<{ children?: React.ReactNode; className?: string }> = ({ children, className }) => (
  <EnhancedBackground variant="garage" className={className}>{children}</EnhancedBackground>
);

export const TrackBackground: React.FC<{ children?: React.ReactNode; className?: string }> = ({ children, className }) => (
  <EnhancedBackground variant="track" className={className}>{children}</EnhancedBackground>
);

export const ShowroomBackground: React.FC<{ children?: React.ReactNode; className?: string }> = ({ children, className }) => (
  <EnhancedBackground variant="showroom" className={className}>{children}</EnhancedBackground>
);

export const OutbackBackground: React.FC<{ children?: React.ReactNode; className?: string }> = ({ children, className }) => (
  <EnhancedBackground variant="outback" className={className}>{children}</EnhancedBackground>
);

export const CityBackground: React.FC<{ children?: React.ReactNode; className?: string }> = ({ children, className }) => (
  <EnhancedBackground variant="city" className={className}>{children}</EnhancedBackground>
);

export const SunsetBackground: React.FC<{ children?: React.ReactNode; className?: string }> = ({ children, className }) => (
  <EnhancedBackground variant="sunset" className={className}>{children}</EnhancedBackground>
);

export default EnhancedBackground;
