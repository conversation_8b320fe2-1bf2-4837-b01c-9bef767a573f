import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  MessageCircle, 
  Book, 
  HelpCircle, 
  Mail, 
  Phone, 
  Clock,
  CheckCircle,
  AlertCircle,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import InteractiveTutorial from '@/components/tutorial/InteractiveTutorial';
import EducationalContent from '@/components/education/EducationalContent';
import { useToast } from '@/components/ui/use-toast';
import AustralianEmblem from '@/components/AustralianEmblem';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: 'general' | 'gameplay' | 'technical' | 'account';
  tags: string[];
}

interface SupportTicket {
  id: string;
  subject: string;
  message: string;
  category: string;
  priority: 'low' | 'medium' | 'high';
  status: 'open' | 'in-progress' | 'resolved';
  timestamp: number;
}

const FAQ_DATA: FAQItem[] = [
  {
    id: 'faq_1',
    question: 'How do I get started with Aussie Burnout Legends?',
    answer: 'Welcome to Aussie Burnout Legends! Start by creating an account to claim your free Founders Edition Mazda RX-3. Then visit your garage to customize your car, head to the Play Game section to practice your skills, and join events to compete with other players.',
    category: 'general',
    tags: ['getting started', 'tutorial', 'basics']
  },
  {
    id: 'faq_2',
    question: 'What are the controls for the driving game?',
    answer: 'Use W/↑ for throttle, S/↓ for brake, A/← and D/→ for steering, and Space for handbrake. For burnouts, hold throttle and brake simultaneously while stationary. For drifts, use throttle and steering with occasional handbrake inputs.',
    category: 'gameplay',
    tags: ['controls', 'driving', 'burnout', 'drift']
  },
  {
    id: 'faq_3',
    question: 'How do I unlock new cars?',
    answer: 'You can unlock new cars by: 1) Purchasing them from the garage shop, 2) Winning events and competitions, 3) Unlocking achievements, or 4) Trading with other players (coming soon). Each car has different performance characteristics.',
    category: 'gameplay',
    tags: ['cars', 'unlock', 'garage', 'shop']
  },
  {
    id: 'faq_4',
    question: 'What is the Founders Edition and how do I get it?',
    answer: 'The Founders Edition is a special status for early supporters of Aussie Burnout Legends. Founders get exclusive cars, early access to events, special badges, and unique customization options. Register now to claim your Founders status!',
    category: 'account',
    tags: ['founders', 'exclusive', 'benefits']
  },
  {
    id: 'faq_5',
    question: 'Why is my game running slowly?',
    answer: 'Performance issues can be caused by: 1) Browser compatibility (try Chrome or Firefox), 2) Hardware limitations, 3) Background applications, or 4) Internet connection. Try closing other tabs, updating your browser, or lowering graphics settings.',
    category: 'technical',
    tags: ['performance', 'lag', 'browser', 'optimization']
  },
  {
    id: 'faq_6',
    question: 'How do achievements work?',
    answer: 'Achievements are unlocked by completing specific challenges like your first burnout, reaching high scores, or participating in events. Each achievement rewards you with points, badges, or sometimes exclusive cars and customization options.',
    category: 'gameplay',
    tags: ['achievements', 'rewards', 'challenges']
  },
  {
    id: 'faq_7',
    question: 'Can I play with friends?',
    answer: 'Currently, Aussie Burnout Legends focuses on individual gameplay and leaderboards. However, multiplayer features including real-time competitions and friend challenges are planned for future updates!',
    category: 'general',
    tags: ['multiplayer', 'friends', 'social']
  },
  {
    id: 'faq_8',
    question: 'How do I report a bug or issue?',
    answer: 'You can report bugs through the Support tab on this page, or email us <NAME_EMAIL>. Please include details about what happened, your browser, and any error messages you saw.',
    category: 'technical',
    tags: ['bugs', 'issues', 'support', 'reporting']
  }
];

const Help: React.FC = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [showTutorial, setShowTutorial] = useState(false);
  const [supportForm, setSupportForm] = useState({
    subject: '',
    category: 'general',
    priority: 'medium',
    message: ''
  });

  const filteredFAQ = FAQ_DATA.filter(item => {
    const matchesSearch = searchQuery === '' || 
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const handleSupportSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!supportForm.subject || !supportForm.message) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Create support ticket
    const ticket: SupportTicket = {
      id: `ticket_${Date.now()}`,
      subject: supportForm.subject,
      message: supportForm.message,
      category: supportForm.category,
      priority: supportForm.priority as 'low' | 'medium' | 'high',
      status: 'open',
      timestamp: Date.now()
    };

    // Store ticket (in real app, this would go to backend)
    const existingTickets = JSON.parse(localStorage.getItem('support_tickets') || '[]');
    existingTickets.push(ticket);
    localStorage.setItem('support_tickets', JSON.stringify(existingTickets));

    toast({
      title: "Support Ticket Created",
      description: `Your ticket #${ticket.id} has been submitted. We'll get back to you soon!`,
      className: "bg-racing-black border-racing-yellow text-white",
    });

    // Reset form
    setSupportForm({
      subject: '',
      category: 'general',
      priority: 'medium',
      message: ''
    });
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'general': return <HelpCircle className="w-4 h-4" />;
      case 'gameplay': return <Book className="w-4 h-4" />;
      case 'technical': return <AlertCircle className="w-4 h-4" />;
      case 'account': return <CheckCircle className="w-4 h-4" />;
      default: return <HelpCircle className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'general': return 'bg-blue-500';
      case 'gameplay': return 'bg-green-500';
      case 'technical': return 'bg-red-500';
      case 'account': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#000424] to-[#001440]">
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">Help & Support</h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Get help with Aussie Burnout Legends. Find answers to common questions, 
            learn how to play, or contact our support team.
          </p>
        </div>

        <Tabs defaultValue="faq" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full max-w-2xl mx-auto">
            <TabsTrigger value="faq">FAQ</TabsTrigger>
            <TabsTrigger value="tutorial">Tutorial</TabsTrigger>
            <TabsTrigger value="guides">Guides</TabsTrigger>
            <TabsTrigger value="support">Support</TabsTrigger>
          </TabsList>

          <TabsContent value="faq">
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-racing-yellow">Frequently Asked Questions</CardTitle>
                    <CardDescription>Find quick answers to common questions</CardDescription>
                  </div>
                  <AustralianEmblem size="sm" className="opacity-70" />
                </div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Search and Filter */}
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search FAQ..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 bg-gray-800 border-gray-700"
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      variant={selectedCategory === 'all' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory('all')}
                    >
                      All
                    </Button>
                    <Button
                      variant={selectedCategory === 'general' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory('general')}
                    >
                      General
                    </Button>
                    <Button
                      variant={selectedCategory === 'gameplay' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory('gameplay')}
                    >
                      Gameplay
                    </Button>
                    <Button
                      variant={selectedCategory === 'technical' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory('technical')}
                    >
                      Technical
                    </Button>
                    <Button
                      variant={selectedCategory === 'account' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCategory('account')}
                    >
                      Account
                    </Button>
                  </div>
                </div>

                {/* FAQ Items */}
                <div className="space-y-4">
                  {filteredFAQ.map((item) => (
                    <Card key={item.id} className="bg-gray-900/40 border-gray-700">
                      <CardContent className="p-0">
                        <button
                          onClick={() => setExpandedFAQ(expandedFAQ === item.id ? null : item.id)}
                          className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-800/40 transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded ${getCategoryColor(item.category)}`}>
                              {getCategoryIcon(item.category)}
                            </div>
                            <h3 className="font-semibold text-white">{item.question}</h3>
                          </div>
                          {expandedFAQ === item.id ? (
                            <ChevronDown className="w-5 h-5 text-gray-400" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-gray-400" />
                          )}
                        </button>
                        
                        {expandedFAQ === item.id && (
                          <div className="px-4 pb-4">
                            <div className="bg-gray-800/40 p-4 rounded-lg">
                              <p className="text-gray-300 mb-3">{item.answer}</p>
                              <div className="flex flex-wrap gap-1">
                                {item.tags.map((tag) => (
                                  <Badge key={tag} variant="outline" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                  
                  {filteredFAQ.length === 0 && (
                    <div className="text-center py-8">
                      <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-xl text-white mb-2">No Results Found</h3>
                      <p className="text-gray-400">Try adjusting your search or browse different categories.</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tutorial">
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <CardTitle className="text-racing-yellow">Interactive Tutorial</CardTitle>
                <CardDescription>Learn how to use Aussie Burnout Legends step by step</CardDescription>
              </CardHeader>
              
              <CardContent className="text-center py-8">
                <Book className="w-16 h-16 text-racing-yellow mx-auto mb-4" />
                <h3 className="text-xl text-white mb-4">Ready to Learn?</h3>
                <p className="text-gray-400 mb-6 max-w-lg mx-auto">
                  Take our interactive tutorial to learn the basics of car customization, 
                  driving controls, achievements, and events.
                </p>
                <Button
                  onClick={() => setShowTutorial(true)}
                  className="bg-racing-red hover:bg-red-700 text-white"
                >
                  Start Tutorial
                </Button>
              </CardContent>
            </Card>
            
            {showTutorial && (
              <InteractiveTutorial
                onComplete={() => setShowTutorial(false)}
                onSkip={() => setShowTutorial(false)}
              />
            )}
          </TabsContent>

          <TabsContent value="guides">
            <EducationalContent />
          </TabsContent>

          <TabsContent value="support">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Contact Form */}
              <Card className="bg-black/40 border-racing-yellow/20">
                <CardHeader>
                  <CardTitle className="text-racing-yellow">Contact Support</CardTitle>
                  <CardDescription>Send us a message and we'll get back to you</CardDescription>
                </CardHeader>
                
                <CardContent>
                  <form onSubmit={handleSupportSubmit} className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-white mb-2 block">Subject *</label>
                      <Input
                        value={supportForm.subject}
                        onChange={(e) => setSupportForm({...supportForm, subject: e.target.value})}
                        placeholder="Brief description of your issue"
                        className="bg-gray-800 border-gray-700"
                        required
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">Category</label>
                        <select
                          value={supportForm.category}
                          onChange={(e) => setSupportForm({...supportForm, category: e.target.value})}
                          className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white"
                        >
                          <option value="general">General</option>
                          <option value="gameplay">Gameplay</option>
                          <option value="technical">Technical</option>
                          <option value="account">Account</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">Priority</label>
                        <select
                          value={supportForm.priority}
                          onChange={(e) => setSupportForm({...supportForm, priority: e.target.value})}
                          className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white"
                        >
                          <option value="low">Low</option>
                          <option value="medium">Medium</option>
                          <option value="high">High</option>
                        </select>
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-white mb-2 block">Message *</label>
                      <Textarea
                        value={supportForm.message}
                        onChange={(e) => setSupportForm({...supportForm, message: e.target.value})}
                        placeholder="Please describe your issue in detail..."
                        className="bg-gray-800 border-gray-700 min-h-[120px]"
                        required
                      />
                    </div>
                    
                    <Button type="submit" className="w-full bg-racing-red hover:bg-red-700 text-white">
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card className="bg-black/40 border-racing-yellow/20">
                <CardHeader>
                  <CardTitle className="text-racing-yellow">Other Ways to Reach Us</CardTitle>
                  <CardDescription>Alternative contact methods and information</CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <div className="flex items-center gap-3">
                    <Mail className="w-5 h-5 text-racing-yellow" />
                    <div>
                      <h4 className="font-semibold text-white">Email Support</h4>
                      <p className="text-gray-400"><EMAIL></p>
                      <p className="text-xs text-gray-500">Response within 24 hours</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Phone className="w-5 h-5 text-racing-yellow" />
                    <div>
                      <h4 className="font-semibold text-white">Phone Support</h4>
                      <p className="text-gray-400">+61 1800 BURNOUT</p>
                      <p className="text-xs text-gray-500">Mon-Fri 9AM-5PM AEST</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-racing-yellow" />
                    <div>
                      <h4 className="font-semibold text-white">Support Hours</h4>
                      <p className="text-gray-400">Monday - Friday: 9AM - 5PM AEST</p>
                      <p className="text-xs text-gray-500">Emergency support available 24/7</p>
                    </div>
                  </div>
                  
                  <div className="bg-racing-yellow/10 border border-racing-yellow/20 rounded-lg p-4">
                    <h4 className="text-racing-yellow font-semibold mb-2">Before Contacting Support</h4>
                    <ul className="text-sm text-gray-300 space-y-1">
                      <li>• Check the FAQ section above</li>
                      <li>• Try refreshing your browser</li>
                      <li>• Clear your browser cache</li>
                      <li>• Include error messages in your report</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>
      
      <Footer />
    </div>
  );
};

export default Help;
