import { Post } from "../types/post";
import PostCard from "./PostCard";

interface FeedListProps {
  posts: Post[];
}

export default function FeedList({ posts }: FeedListProps) {
  if (!posts.length) {
    return (
      <div className="flex flex-col items-center justify-center py-20">
        <div className="skeleton w-32 h-32 mb-4" />
        <p className="text-gray-500">No posts yet. Check back soon!</p>
      </div>
    );
  }

  return (
    <section>
      {posts.map(post => (
        <PostCard key={post.id} post={post} />
      ))}
    </section>
  );
}
