import React, { useState, useEffect, ReactNode } from 'react';

interface LazyLoadProps {
  children: ReactNode;
  placeholder?: ReactNode;
  threshold?: number; // Visibility threshold (0-1)
  rootMargin?: string; // Margin around the root
}

const LazyLoad: React.FC<LazyLoadProps> = ({
  children,
  placeholder = <div className="animate-pulse bg-gray-800 rounded-lg h-40"></div>,
  threshold = 0.1,
  rootMargin = '100px',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [element, setElement] = useState<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
          observer.disconnect();
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, [element, hasLoaded, threshold, rootMargin]);

  return (
    <div ref={setElement} className="w-full">
      {isVisible ? children : placeholder}
    </div>
  );
};

export default LazyLoad;
