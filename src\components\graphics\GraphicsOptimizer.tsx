import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface GraphicsSettings {
  quality: 'low' | 'medium' | 'high' | 'ultra';
  animations: boolean;
  particles: boolean;
  shadows: boolean;
  reflections: boolean;
  antialiasing: boolean;
  motionReduce: boolean;
  autoOptimize: boolean;
}

interface GraphicsContextType {
  settings: GraphicsSettings;
  updateSettings: (newSettings: Partial<GraphicsSettings>) => void;
  getOptimizedImageUrl: (url: string, width?: number, height?: number) => string;
  shouldRenderEffect: (effectType: keyof GraphicsSettings) => boolean;
  performanceLevel: 'low' | 'medium' | 'high';
}

const GraphicsContext = createContext<GraphicsContextType | undefined>(undefined);

export const useGraphics = () => {
  const context = useContext(GraphicsContext);
  if (!context) {
    throw new Error('useGraphics must be used within a GraphicsProvider');
  }
  return context;
};

interface GraphicsProviderProps {
  children: ReactNode;
}

export const GraphicsProvider: React.FC<GraphicsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<GraphicsSettings>(() => {
    // Load settings from localStorage or use defaults
    const saved = localStorage.getItem('aussie-burnout-graphics');
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch {
        // Fall through to defaults
      }
    }
    
    return {
      quality: 'high',
      animations: true,
      particles: true,
      shadows: true,
      reflections: true,
      antialiasing: true,
      motionReduce: false,
      autoOptimize: true
    };
  });

  const [performanceLevel, setPerformanceLevel] = useState<'low' | 'medium' | 'high'>('high');

  // Auto-detect performance level
  useEffect(() => {
    if (!settings.autoOptimize) return;

    const detectPerformance = () => {
      // Check device capabilities
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      let level: 'low' | 'medium' | 'high' = 'medium';
      
      // Check for WebGL support
      if (!gl) {
        level = 'low';
      } else {
        // Check GPU capabilities
        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        if (debugInfo) {
          const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
          
          // Basic GPU detection (simplified)
          if (renderer.includes('Intel') && !renderer.includes('Iris')) {
            level = 'low';
          } else if (renderer.includes('GTX') || renderer.includes('RTX') || renderer.includes('Radeon')) {
            level = 'high';
          }
        }
      }
      
      // Check memory
      if ('memory' in performance && (performance as any).memory) {
        const memory = (performance as any).memory;
        if (memory.usedJSHeapSize / memory.totalJSHeapSize > 0.8) {
          level = level === 'high' ? 'medium' : 'low';
        }
      }
      
      // Check connection speed
      if ('connection' in navigator && (navigator as any).connection) {
        const connection = (navigator as any).connection;
        if (connection.effectiveType === '2g' || connection.effectiveType === 'slow-2g') {
          level = 'low';
        }
      }
      
      // Check device pixel ratio
      if (window.devicePixelRatio > 2) {
        level = level === 'low' ? 'low' : 'medium'; // High DPI can impact performance
      }
      
      setPerformanceLevel(level);
      
      // Auto-adjust settings based on performance
      if (level === 'low') {
        setSettings(prev => ({
          ...prev,
          quality: 'medium',
          particles: false,
          shadows: false,
          reflections: false,
          antialiasing: false
        }));
      } else if (level === 'medium') {
        setSettings(prev => ({
          ...prev,
          quality: 'high',
          particles: true,
          shadows: true,
          reflections: false,
          antialiasing: true
        }));
      }
    };

    detectPerformance();
  }, [settings.autoOptimize]);

  // Check for reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleChange = () => {
      setSettings(prev => ({
        ...prev,
        motionReduce: mediaQuery.matches,
        animations: !mediaQuery.matches && prev.animations
      }));
    };
    
    handleChange();
    mediaQuery.addEventListener('change', handleChange);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Save settings to localStorage
  useEffect(() => {
    localStorage.setItem('aussie-burnout-graphics', JSON.stringify(settings));
  }, [settings]);

  const updateSettings = (newSettings: Partial<GraphicsSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const getOptimizedImageUrl = (url: string, width?: number, height?: number): string => {
    if (!url.includes('unsplash.com')) return url;
    
    try {
      const urlObj = new URL(url);
      
      // Set quality based on settings
      const qualityMap = {
        low: '60',
        medium: '75',
        high: '85',
        ultra: '95'
      };
      urlObj.searchParams.set('q', qualityMap[settings.quality]);
      
      // Set dimensions
      if (width) {
        // Adjust width based on device pixel ratio and performance
        const adjustedWidth = performanceLevel === 'low' 
          ? Math.min(width, 800)
          : performanceLevel === 'medium'
          ? Math.min(width * window.devicePixelRatio, 1200)
          : width * window.devicePixelRatio;
        
        urlObj.searchParams.set('w', Math.round(adjustedWidth).toString());
      }
      
      if (height) {
        const adjustedHeight = performanceLevel === 'low'
          ? Math.min(height, 600)
          : performanceLevel === 'medium'
          ? Math.min(height * window.devicePixelRatio, 900)
          : height * window.devicePixelRatio;
        
        urlObj.searchParams.set('h', Math.round(adjustedHeight).toString());
      }
      
      // Set format optimization
      urlObj.searchParams.set('fm', 'webp');
      urlObj.searchParams.set('auto', 'format,compress');
      
      // Add fit parameter for better cropping
      urlObj.searchParams.set('fit', 'crop');
      
      return urlObj.toString();
    } catch {
      return url;
    }
  };

  const shouldRenderEffect = (effectType: keyof GraphicsSettings): boolean => {
    if (settings.motionReduce && (effectType === 'animations' || effectType === 'particles')) {
      return false;
    }
    
    return settings[effectType] as boolean;
  };

  const value: GraphicsContextType = {
    settings,
    updateSettings,
    getOptimizedImageUrl,
    shouldRenderEffect,
    performanceLevel
  };

  return (
    <GraphicsContext.Provider value={value}>
      {children}
    </GraphicsContext.Provider>
  );
};

// Graphics Settings Panel Component
export const GraphicsSettingsPanel: React.FC<{ 
  isOpen: boolean; 
  onClose: () => void; 
}> = ({ isOpen, onClose }) => {
  const { settings, updateSettings, performanceLevel } = useGraphics();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 border border-racing-yellow/30 rounded-lg p-6 max-w-md w-full">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">Graphics Settings</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            ✕
          </button>
        </div>

        <div className="space-y-4">
          {/* Performance Level Indicator */}
          <div className="p-3 bg-gray-800 rounded border border-gray-700">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">Performance Level:</span>
              <span className={`font-semibold ${
                performanceLevel === 'high' ? 'text-green-400' :
                performanceLevel === 'medium' ? 'text-yellow-400' : 'text-red-400'
              }`}>
                {performanceLevel.toUpperCase()}
              </span>
            </div>
          </div>

          {/* Quality Setting */}
          <div>
            <label className="block text-gray-300 mb-2">Image Quality</label>
            <select
              value={settings.quality}
              onChange={(e) => updateSettings({ quality: e.target.value as any })}
              className="w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white"
            >
              <option value="low">Low (Faster)</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="ultra">Ultra (Slower)</option>
            </select>
          </div>

          {/* Toggle Settings */}
          {[
            { key: 'animations', label: 'Animations' },
            { key: 'particles', label: 'Particle Effects' },
            { key: 'shadows', label: 'Shadows' },
            { key: 'reflections', label: 'Reflections' },
            { key: 'antialiasing', label: 'Anti-aliasing' },
            { key: 'autoOptimize', label: 'Auto Optimize' }
          ].map(({ key, label }) => (
            <div key={key} className="flex justify-between items-center">
              <span className="text-gray-300">{label}</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings[key as keyof GraphicsSettings] as boolean}
                  onChange={(e) => updateSettings({ [key]: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-racing-yellow"></div>
              </label>
            </div>
          ))}

          {/* Reduced Motion Warning */}
          {settings.motionReduce && (
            <div className="p-3 bg-yellow-900/30 border border-yellow-500/30 rounded">
              <p className="text-yellow-400 text-sm">
                ⚠️ Reduced motion is enabled in your system preferences. Some animations are disabled.
              </p>
            </div>
          )}
        </div>

        <div className="mt-6 flex gap-3">
          <button
            onClick={() => {
              updateSettings({
                quality: 'low',
                animations: false,
                particles: false,
                shadows: false,
                reflections: false,
                antialiasing: false
              });
            }}
            className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded transition-colors"
          >
            Performance Mode
          </button>
          <button
            onClick={() => {
              updateSettings({
                quality: 'ultra',
                animations: true,
                particles: true,
                shadows: true,
                reflections: true,
                antialiasing: true
              });
            }}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded transition-colors"
          >
            Quality Mode
          </button>
        </div>
      </div>
    </div>
  );
};

// Performance Monitor Component
export const PerformanceMonitor: React.FC = () => {
  const { performanceLevel } = useGraphics();
  const [fps, setFps] = useState(60);
  const [memoryUsage, setMemoryUsage] = useState(0);

  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
        
        // Measure memory usage if available
        if ('memory' in performance && (performance as any).memory) {
          const memory = (performance as any).memory;
          setMemoryUsage(Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100));
        }
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);
  }, []);

  return (
    <div className="fixed bottom-4 left-4 bg-black/80 text-white text-xs p-2 rounded font-mono z-50">
      <div>FPS: {fps}</div>
      <div>Performance: {performanceLevel}</div>
      {memoryUsage > 0 && <div>Memory: {memoryUsage}%</div>}
    </div>
  );
};

export default GraphicsProvider;
