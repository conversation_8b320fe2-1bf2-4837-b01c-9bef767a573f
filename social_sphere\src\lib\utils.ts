export const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
  });
};

export const mockPosts: Post[] = [
  {
    id: '1',
    content: 'Just launched my new project! #coding #webdev',
    author: {
      id: '1',
      name: '<PERSON>',
      username: '@johndoe',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    },
    likes: 42,
    comments: 12,
    createdAt: new Date().toISOString(),
  },
  // Add more mock posts as needed
];
