import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import AustralianEmblem from '@/components/AustralianEmblem';

interface CarCustomization {
  bodyColor: string;
  hasRacingStripes: boolean;
  hasAustralianFlag: boolean;
  wheelSize: number;
  exhaustType: string;
}

interface CarCustomizerProps {
  className?: string;
  customization?: CarCustomization;
  onCustomizationChange?: (customization: CarCustomization) => void;
}

const CarCustomizer: React.FC<CarCustomizerProps> = ({
  className,
  customization,
  onCustomizationChange
}) => {
  const { toast } = useToast();

  // Use props or default values
  const bodyColor = customization?.bodyColor || '#FF0000';
  const wheelSize = customization?.wheelSize || 17;
  const exhaustType = customization?.exhaustType || 'standard';
  const hasRacingStripes = customization?.hasRacingStripes || false;
  const hasAustralianFlag = customization?.hasAustralianFlag || true;

  // Helper function to update customization
  const updateCustomization = (updates: Partial<CarCustomization>) => {
    if (onCustomizationChange) {
      onCustomizationChange({
        bodyColor,
        wheelSize,
        exhaustType,
        hasRacingStripes,
        hasAustralianFlag,
        ...updates
      });
    }
  };

  const handleSaveConfig = () => {
    toast({
      title: "Configuration Saved!",
      description: "Your custom RX-3 configuration has been saved.",
      className: "bg-racing-black border-racing-yellow text-white",
    });
  };

  const handleResetConfig = () => {
    updateCustomization({
      bodyColor: '#FF0000',
      wheelSize: 17,
      exhaustType: 'standard',
      hasRacingStripes: false,
      hasAustralianFlag: true
    });

    toast({
      title: "Configuration Reset",
      description: "Your RX-3 has been reset to default settings.",
      variant: "destructive",
    });
  };

  return (
    <div className={`${className}`}>
      <Card className="bg-black/40 border-racing-yellow/20">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-2xl text-racing-yellow">Car Customizer</CardTitle>
            <AustralianEmblem size="sm" className="opacity-70" />
          </div>
          <CardDescription className="text-gray-400">
            Customize your Founders Edition Mazda RX-3 Savanna
          </CardDescription>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="exterior" className="w-full">
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="exterior">Exterior</TabsTrigger>
              <TabsTrigger value="wheels">Wheels</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
            </TabsList>

            <TabsContent value="exterior" className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-white mb-2">Body Color</h3>
                <div className="grid grid-cols-5 gap-2">
                  {['#FF0000', '#00008B', '#FFFFFF', '#000000', '#FFD700'].map((color) => (
                    <div
                      key={color}
                      className={`w-full aspect-square rounded-md cursor-pointer border-2 ${bodyColor === color ? 'border-white' : 'border-transparent'}`}
                      style={{ backgroundColor: color }}
                      onClick={() => updateCustomization({ bodyColor: color })}
                    />
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {bodyColor === '#FF0000' ? 'Racing Red' :
                   bodyColor === '#00008B' ? 'Australian Navy Blue' :
                   bodyColor === '#FFFFFF' ? 'Pure White' :
                   bodyColor === '#000000' ? 'Midnight Black' : 'Gold Rush'}
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="racing-stripes" className="text-sm font-medium text-white">
                    Racing Stripes
                  </Label>
                  <Switch
                    id="racing-stripes"
                    checked={hasRacingStripes}
                    onCheckedChange={(checked) => updateCustomization({ hasRacingStripes: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="australian-flag" className="text-sm font-medium text-white">
                    Australian Flag Emblem
                  </Label>
                  <Switch
                    id="australian-flag"
                    checked={hasAustralianFlag}
                    onCheckedChange={(checked) => updateCustomization({ hasAustralianFlag: checked })}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="wheels" className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-white mb-2">Wheel Size</h3>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-400">15"</span>
                  <Slider
                    value={[wheelSize]}
                    min={15}
                    max={19}
                    step={1}
                    onValueChange={(value) => updateCustomization({ wheelSize: value[0] })}
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-400">19"</span>
                </div>
                <p className="text-center text-sm text-white mt-2">{wheelSize}"</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-white mb-2">Wheel Style</h3>
                <div className="grid grid-cols-3 gap-2">
                  <div className="bg-gray-800 rounded-md p-2 text-center cursor-pointer border border-white">
                    <div className="w-full aspect-square rounded-full bg-gray-700 border-8 border-gray-600 mb-1"></div>
                    <span className="text-xs text-gray-300">Classic</span>
                  </div>
                  <div className="bg-gray-800 rounded-md p-2 text-center cursor-pointer">
                    <div className="w-full aspect-square rounded-full bg-gray-700 flex items-center justify-center mb-1">
                      <div className="w-3/4 h-3/4 rounded-full border-8 border-gray-600"></div>
                    </div>
                    <span className="text-xs text-gray-300">Sport</span>
                  </div>
                  <div className="bg-gray-800 rounded-md p-2 text-center cursor-pointer">
                    <div className="w-full aspect-square rounded-full bg-gray-700 flex items-center justify-center mb-1">
                      <div className="w-4/5 h-4/5 rounded-full border-[6px] border-gray-600 flex items-center justify-center">
                        <div className="w-1/2 h-1/2 rounded-full bg-gray-600"></div>
                      </div>
                    </div>
                    <span className="text-xs text-gray-300">Racing</span>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-white mb-2">Exhaust System</h3>
                <RadioGroup value={exhaustType} onValueChange={(value) => updateCustomization({ exhaustType: value })} className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="standard" id="exhaust-standard" />
                    <Label htmlFor="exhaust-standard" className="text-gray-300">Standard</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="sport" id="exhaust-sport" />
                    <Label htmlFor="exhaust-sport" className="text-gray-300">Sport</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="racing" id="exhaust-racing" />
                    <Label htmlFor="exhaust-racing" className="text-gray-300">Racing</Label>
                  </div>
                </RadioGroup>
              </div>

              <div>
                <h3 className="text-sm font-medium text-white mb-2">Engine Tuning</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-xs text-gray-400 mb-1">
                      <span>Power</span>
                      <span>+15%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div className="bg-racing-red h-2 rounded-full" style={{ width: '60%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-xs text-gray-400 mb-1">
                      <span>Torque</span>
                      <span>+10%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div className="bg-racing-red h-2 rounded-full" style={{ width: '45%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-xs text-gray-400 mb-1">
                      <span>Handling</span>
                      <span>+5%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div className="bg-racing-red h-2 rounded-full" style={{ width: '30%' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={handleResetConfig}>
            Reset
          </Button>
          <Button className="bg-racing-red hover:bg-red-700 text-white" onClick={handleSaveConfig}>
            Save Configuration
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default CarCustomizer;
