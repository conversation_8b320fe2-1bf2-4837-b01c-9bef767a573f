
import React, { useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera, Environment, ContactShadows } from '@react-three/drei';
import { Group } from 'three';

interface CarCustomization {
  bodyColor?: string;
  hasRacingStripes?: boolean;
  hasAustralianFlag?: boolean;
  wheelSize?: number;
  exhaustType?: string;
}

// RX-3 car model - more detailed model of the Mazda RX-3
const RX3Model = ({
  rotationSpeed = 0.002,
  isRevving = false,
  customization = {}
}) => {
  const group = useRef<Group>(null);
  const [hovering, setHovering] = useState(false);

  // Rotation animation
  useFrame(() => {
    if (group.current) {
      group.current.rotation.y += rotationSpeed;

      // Add slight shake when revving
      if (isRevving && group.current) {
        group.current.position.y = Math.sin(Date.now() * 0.05) * 0.02;
      }
    }
  });

  // Colors - Australian flag theme with customization
  const bodyColor = customization.bodyColor || (hovering ? '#00008B' : '#FF0000');
  const wheelColor = '#1A1A1A';
  const glassColor = '#ADD8E6';
  const chromeTrim = '#C0C0C0';
  const detailsColor = '#FFFFFF'; // White for details
  const wheelScale = customization.wheelSize ? (customization.wheelSize / 17) : 1;

  return (
    <group
      ref={group}
      onPointerOver={() => setHovering(true)}
      onPointerOut={() => setHovering(false)}
      position={[0, -1, 0]} // Lower the car position a bit
    >
      {/* Main car body - RX-3 Savanna */}
      <mesh castShadow receiveShadow>
        {/* Lower body/chassis */}
        <boxGeometry args={[4.2, 0.8, 1.8]} />
        <meshStandardMaterial color={bodyColor} metalness={0.6} roughness={0.2} />

        {/* Upper body/cabin - more coupe-like for RX-3 */}
        <mesh position={[-0.2, 0.8, 0]} castShadow>
          <boxGeometry args={[3, 0.8, 1.6]} />
          <meshStandardMaterial color={bodyColor} metalness={0.6} roughness={0.2} />
        </mesh>

        {/* Front hood - longer for RX-3 */}
        <mesh position={[1.4, 0.5, 0]} castShadow>
          <boxGeometry args={[1.4, 0.2, 1.7]} />
          <meshStandardMaterial color={bodyColor} metalness={0.6} roughness={0.2} />
        </mesh>

        {/* Trunk */}
        <mesh position={[-1.8, 0.5, 0]} castShadow>
          <boxGeometry args={[0.6, 0.2, 1.7]} />
          <meshStandardMaterial color={bodyColor} metalness={0.6} roughness={0.2} />
        </mesh>

        {/* Windshield */}
        <mesh position={[0.6, 1, 0]} castShadow rotation={[0.3, 0, 0]}>
          <planeGeometry args={[1.2, 0.8]} />
          <meshStandardMaterial color={glassColor} metalness={0.9} roughness={0.1} transparent opacity={0.7} />
        </mesh>

        {/* Rear window */}
        <mesh position={[-1, 1, 0]} castShadow rotation={[-0.3, 0, 0]}>
          <planeGeometry args={[1, 0.7]} />
          <meshStandardMaterial color={glassColor} metalness={0.9} roughness={0.1} transparent opacity={0.7} />
        </mesh>

        {/* Headlights - round for RX-3 */}
        <mesh position={[2.1, 0.5, 0.6]} castShadow>
          <cylinderGeometry args={[0.25, 0.25, 0.1, 16]} />
          <meshStandardMaterial color="#FFFFFF" emissive="#FFFFAA" emissiveIntensity={hovering ? 0.5 : 0} />
        </mesh>
        <mesh position={[2.1, 0.5, -0.6]} castShadow>
          <cylinderGeometry args={[0.25, 0.25, 0.1, 16]} />
          <meshStandardMaterial color="#FFFFFF" emissive="#FFFFAA" emissiveIntensity={hovering ? 0.5 : 0} />
        </mesh>

        {/* Taillights */}
        <mesh position={[-2.1, 0.5, 0.6]} castShadow>
          <boxGeometry args={[0.1, 0.2, 0.3]} />
          <meshStandardMaterial color="#8B0000" emissive="#FF0000" emissiveIntensity={hovering ? 0.5 : 0} />
        </mesh>
        <mesh position={[-2.1, 0.5, -0.6]} castShadow>
          <boxGeometry args={[0.1, 0.2, 0.3]} />
          <meshStandardMaterial color="#8B0000" emissive="#FF0000" emissiveIntensity={hovering ? 0.5 : 0} />
        </mesh>

        {/* Grille - distinctive RX-3 grille */}
        <mesh position={[2.1, 0.4, 0]} castShadow>
          <boxGeometry args={[0.1, 0.3, 1]} />
          <meshStandardMaterial color={chromeTrim} metalness={0.9} roughness={0.3} />
        </mesh>

        {/* Australian flag star (Southern Cross) on roof */}
        <mesh position={[0, 1.2, 0]} rotation={[-Math.PI / 2, 0, 0]}>
          <circleGeometry args={[0.4, 5]} />
          <meshStandardMaterial color={detailsColor} />
        </mesh>

        {/* Bumpers */}
        <mesh position={[2.15, 0, 0]} castShadow>
          <boxGeometry args={[0.1, 0.3, 1.6]} />
          <meshStandardMaterial color={chromeTrim} metalness={0.8} roughness={0.2} />
        </mesh>
        <mesh position={[-2.15, 0, 0]} castShadow>
          <boxGeometry args={[0.1, 0.3, 1.6]} />
          <meshStandardMaterial color={chromeTrim} metalness={0.8} roughness={0.2} />
        </mesh>

        {/* Side windows */}
        <mesh position={[0, 0.9, 0.9]} rotation={[0, 0, 0]} castShadow>
          <planeGeometry args={[2.4, 0.6]} />
          <meshStandardMaterial color={glassColor} metalness={0.9} roughness={0.1} transparent opacity={0.6} side={2} />
        </mesh>
        <mesh position={[0, 0.9, -0.9]} rotation={[0, 0, 0]} castShadow>
          <planeGeometry args={[2.4, 0.6]} />
          <meshStandardMaterial color={glassColor} metalness={0.9} roughness={0.1} transparent opacity={0.6} side={2} />
        </mesh>

        {/* Australian flag stripes on side */}
        <mesh position={[0, 0.3, 0.901]} castShadow>
          <planeGeometry args={[4, 0.2]} />
          <meshStandardMaterial color="#00008B" /> {/* Dark blue */}
        </mesh>
        <mesh position={[0, 0.3, -0.901]} castShadow>
          <planeGeometry args={[4, 0.2]} />
          <meshStandardMaterial color="#00008B" /> {/* Dark blue */}
        </mesh>

        {/* Racing stripes if enabled */}
        {customization.hasRacingStripes && (
          <>
            <mesh position={[0, 0.81, 0]} castShadow>
              <boxGeometry args={[3, 0.01, 0.3]} />
              <meshStandardMaterial color="#FFFFFF" />
            </mesh>
            <mesh position={[0, 0.81, 0]} castShadow>
              <boxGeometry args={[3, 0.01, 0.1]} />
              <meshStandardMaterial color="#FF0000" />
            </mesh>
          </>
        )}
      </mesh>

      {/* Wheels - positioned for RX-3 wheel base with customization */}
      <mesh position={[1.4, -0.2, 0.9]} castShadow scale={[wheelScale, wheelScale, 1]}>
        <cylinderGeometry args={[0.4, 0.4, 0.2, 24]} />
        <meshStandardMaterial color={wheelColor} metalness={0.5} roughness={0.7} />
        <mesh position={[0, 0, 0]} castShadow>
          <cylinderGeometry args={[0.2, 0.2, 0.21, 24]} />
          <meshStandardMaterial color={chromeTrim} metalness={0.8} roughness={0.2} />
        </mesh>
      </mesh>
      <mesh position={[1.4, -0.2, -0.9]} castShadow scale={[wheelScale, wheelScale, 1]}>
        <cylinderGeometry args={[0.4, 0.4, 0.2, 24]} />
        <meshStandardMaterial color={wheelColor} metalness={0.5} roughness={0.7} />
        <mesh position={[0, 0, 0]} castShadow>
          <cylinderGeometry args={[0.2, 0.2, 0.21, 24]} />
          <meshStandardMaterial color={chromeTrim} metalness={0.8} roughness={0.2} />
        </mesh>
      </mesh>
      <mesh position={[-1.4, -0.2, 0.9]} castShadow scale={[wheelScale, wheelScale, 1]}>
        <cylinderGeometry args={[0.4, 0.4, 0.2, 24]} />
        <meshStandardMaterial color={wheelColor} metalness={0.5} roughness={0.7} />
        <mesh position={[0, 0, 0]} castShadow>
          <cylinderGeometry args={[0.2, 0.2, 0.21, 24]} />
          <meshStandardMaterial color={chromeTrim} metalness={0.8} roughness={0.2} />
        </mesh>
      </mesh>
      <mesh position={[-1.4, -0.2, -0.9]} castShadow scale={[wheelScale, wheelScale, 1]}>
        <cylinderGeometry args={[0.4, 0.4, 0.2, 24]} />
        <meshStandardMaterial color={wheelColor} metalness={0.5} roughness={0.7} />
        <mesh position={[0, 0, 0]} castShadow>
          <cylinderGeometry args={[0.2, 0.2, 0.21, 24]} />
          <meshStandardMaterial color={chromeTrim} metalness={0.8} roughness={0.2} />
        </mesh>
      </mesh>

      {/* "RX-3" badging */}
      <mesh position={[-1.8, 0.7, 0.901]} castShadow>
        <boxGeometry args={[0.4, 0.1, 0.01]} />
        <meshStandardMaterial color={chromeTrim} metalness={1} roughness={0.1} />
      </mesh>

      {/* Australia flag emblem on the hood */}
      <mesh position={[1.5, 0.51, 0]} castShadow>
        <boxGeometry args={[0.5, 0.01, 0.5]} />
        <meshStandardMaterial color="#00008B" metalness={0.3} roughness={0.3} /> {/* Dark blue background */}
      </mesh>

      {/* White stars - simplified representation */}
      <mesh position={[1.5, 0.52, 0]} castShadow>
        <boxGeometry args={[0.1, 0.01, 0.1]} />
        <meshStandardMaterial color="#FFFFFF" metalness={0.3} roughness={0.3} />
      </mesh>
    </group>
  );
};

interface Car3DViewerProps {
  className?: string;
  isRevving?: boolean;
  onRevEngine?: () => void;
  customization?: CarCustomization;
}

const Car3DViewer: React.FC<Car3DViewerProps> = ({
  className,
  isRevving = false,
  onRevEngine,
  customization
}) => {
  return (
    <div className={`w-full h-[400px] ${className} ${isRevving ? 'animate-rev-engine' : ''}`}>
      <Canvas shadows dpr={[1, 2]}>
        <color attach="background" args={['#00008B']} /> {/* Dark blue background - Australian flag style */}
        <PerspectiveCamera makeDefault position={[8, 3, 8]} fov={40} />
        <ambientLight intensity={0.3} />
        <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} intensity={1} castShadow />

        <RX3Model isRevving={isRevving} customization={customization} />

        <ContactShadows
          position={[0, -1.5, 0]}
          opacity={0.4}
          scale={10}
          blur={1.5}
          far={1.5}
        />

        <Environment preset="city" />
        <OrbitControls
          enablePan={false}
          enableZoom={true}
          minPolarAngle={Math.PI / 6}
          maxPolarAngle={Math.PI / 2}
        />
      </Canvas>

      <div className="absolute bottom-3 left-3 bg-black/60 text-white px-3 py-1 rounded-md text-sm">
        Click and drag to rotate • Scroll to zoom
      </div>

      {isRevving && (
        <div className="smoke-animation absolute -bottom-10 left-1/2 -translate-x-1/2"></div>
      )}
    </div>
  );
};

export default Car3DViewer;
