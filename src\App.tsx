
import React, { useEffect } from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { AuthProvider } from "@/context/AuthContext";
import { analytics } from '@/utils/analytics';
import { AnalyticsErrorBoundary } from '@/components/ErrorBoundary';
import PageTransition from '@/components/ui/page-transition';
import { GraphicsProvider } from '@/components/graphics/GraphicsOptimizer';
import Index from "./pages/Index";
import Garage from "./pages/Garage";
import Events from "./pages/Events";
import Leaderboard from "./pages/Leaderboard";
import Register from "./pages/Register";
import PlayGame from "./pages/PlayGame";
import Help from "./pages/Help";
import AdminDashboard from '@/components/admin/AdminDashboard';
import GraphicsShowcase from "./pages/GraphicsShowcase";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

// Analytics tracking component
const AnalyticsTracker: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    // Track page views
    analytics.trackPageView(location.pathname);
  }, [location]);

  return null;
};

const App = () => (
  <AnalyticsErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <GraphicsProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <AnalyticsTracker />
              <PageTransition>
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/garage" element={<Garage />} />
                  <Route path="/events" element={<Events />} />
                  <Route path="/leaderboard" element={<Leaderboard />} />
                  <Route path="/register" element={<Register />} />
                  <Route path="/play" element={<PlayGame />} />
                  <Route path="/help" element={<Help />} />
                  <Route path="/admin" element={<AdminDashboard />} />
                  <Route path="/graphics" element={<GraphicsShowcase />} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </PageTransition>
            </BrowserRouter>
          </TooltipProvider>
        </GraphicsProvider>
      </AuthProvider>
    </QueryClientProvider>
  </AnalyticsErrorBoundary>
);

export default App;
