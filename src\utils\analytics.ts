// Analytics and Error Tracking System
interface AnalyticsEvent {
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  userId?: string;
  timestamp: number;
  sessionId: string;
  userAgent: string;
  url: string;
  referrer: string;
}

interface ErrorReport {
  id: string;
  message: string;
  stack?: string;
  url: string;
  lineNumber?: number;
  columnNumber?: number;
  timestamp: number;
  userId?: string;
  sessionId: string;
  userAgent: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
}

interface UserSession {
  sessionId: string;
  userId?: string;
  startTime: number;
  lastActivity: number;
  pageViews: number;
  events: AnalyticsEvent[];
  errors: ErrorReport[];
  userAgent: string;
  referrer: string;
}

class AnalyticsManager {
  private sessionId: string;
  private userId?: string;
  private session: UserSession;
  private eventQueue: AnalyticsEvent[] = [];
  private errorQueue: ErrorReport[] = [];
  private isEnabled: boolean = true;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.session = this.initializeSession();
    this.setupErrorHandling();
    this.setupPerformanceTracking();
    this.startHeartbeat();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeSession(): UserSession {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      startTime: Date.now(),
      lastActivity: Date.now(),
      pageViews: 0,
      events: [],
      errors: [],
      userAgent: navigator.userAgent,
      referrer: document.referrer
    };
  }

  private setupErrorHandling(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.reportError({
        message: event.message,
        stack: event.error?.stack,
        url: event.filename,
        lineNumber: event.lineno,
        columnNumber: event.colno,
        severity: 'high',
        context: {
          type: 'javascript_error',
          target: event.target?.tagName
        }
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        url: window.location.href,
        severity: 'medium',
        context: {
          type: 'promise_rejection',
          reason: event.reason
        }
      });
    });

    // React error boundary integration
    window.addEventListener('react-error', (event: any) => {
      this.reportError({
        message: event.detail.message,
        stack: event.detail.stack,
        url: window.location.href,
        severity: 'high',
        context: {
          type: 'react_error',
          componentStack: event.detail.componentStack
        }
      });
    });
  }

  private setupPerformanceTracking(): void {
    // Track page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          this.trackEvent({
            event: 'page_performance',
            category: 'performance',
            action: 'page_load',
            value: navigation.loadEventEnd - navigation.loadEventStart,
            label: window.location.pathname
          });
        }
      }, 0);
    });

    // Track Core Web Vitals
    this.trackWebVitals();
  }

  private trackWebVitals(): void {
    // This would integrate with web-vitals library in a real implementation
    // For now, we'll track basic metrics
    if ('PerformanceObserver' in window) {
      // Track Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.trackEvent({
          event: 'web_vital',
          category: 'performance',
          action: 'lcp',
          value: Math.round(lastEntry.startTime),
          label: window.location.pathname
        });
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // Track First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          this.trackEvent({
            event: 'web_vital',
            category: 'performance',
            action: 'fid',
            value: Math.round(entry.processingStart - entry.startTime),
            label: window.location.pathname
          });
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
    }
  }

  private startHeartbeat(): void {
    setInterval(() => {
      this.session.lastActivity = Date.now();
      this.flushQueues();
    }, 30000); // Send data every 30 seconds
  }

  public setUserId(userId: string): void {
    this.userId = userId;
    this.session.userId = userId;
  }

  public trackEvent(eventData: Partial<AnalyticsEvent>): void {
    if (!this.isEnabled) return;

    const event: AnalyticsEvent = {
      event: eventData.event || 'unknown',
      category: eventData.category || 'general',
      action: eventData.action || 'unknown',
      label: eventData.label,
      value: eventData.value,
      userId: this.userId,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      url: window.location.href,
      referrer: document.referrer
    };

    this.eventQueue.push(event);
    this.session.events.push(event);
    this.session.lastActivity = Date.now();

    // Auto-flush if queue gets too large
    if (this.eventQueue.length >= 10) {
      this.flushQueues();
    }
  }

  public reportError(errorData: Partial<ErrorReport>): void {
    if (!this.isEnabled) return;

    const error: ErrorReport = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      message: errorData.message || 'Unknown error',
      stack: errorData.stack,
      url: errorData.url || window.location.href,
      lineNumber: errorData.lineNumber,
      columnNumber: errorData.columnNumber,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      severity: errorData.severity || 'medium',
      context: errorData.context
    };

    this.errorQueue.push(error);
    this.session.errors.push(error);

    // Immediately flush critical errors
    if (error.severity === 'critical') {
      this.flushQueues();
    }

    console.error('Analytics Error Reported:', error);
  }

  public trackPageView(path?: string): void {
    this.session.pageViews++;
    this.trackEvent({
      event: 'page_view',
      category: 'navigation',
      action: 'view',
      label: path || window.location.pathname
    });
  }

  public trackUserAction(action: string, category: string = 'user_interaction', label?: string, value?: number): void {
    this.trackEvent({
      event: 'user_action',
      category,
      action,
      label,
      value
    });
  }

  public trackGameEvent(action: string, label?: string, value?: number): void {
    this.trackEvent({
      event: 'game_event',
      category: 'gameplay',
      action,
      label,
      value
    });
  }

  public trackCustomization(action: string, carModel: string, customizationType: string): void {
    this.trackEvent({
      event: 'customization',
      category: 'car_customization',
      action,
      label: `${carModel}_${customizationType}`
    });
  }

  public trackAchievement(achievementId: string, achievementName: string): void {
    this.trackEvent({
      event: 'achievement_unlocked',
      category: 'achievements',
      action: 'unlock',
      label: achievementId,
      value: 1
    });
  }

  public trackEventRegistration(eventId: string, eventType: string): void {
    this.trackEvent({
      event: 'event_registration',
      category: 'events',
      action: 'register',
      label: `${eventType}_${eventId}`
    });
  }

  private flushQueues(): void {
    if (this.eventQueue.length > 0 || this.errorQueue.length > 0) {
      // In a real implementation, this would send data to your analytics service
      this.sendToAnalyticsService({
        events: [...this.eventQueue],
        errors: [...this.errorQueue],
        session: this.session
      });

      // Clear queues
      this.eventQueue = [];
      this.errorQueue = [];
    }
  }

  private sendToAnalyticsService(data: any): void {
    // In a real implementation, this would send to your analytics backend
    // For now, we'll store in localStorage for demonstration
    try {
      const existingData = JSON.parse(localStorage.getItem('analytics_data') || '[]');
      existingData.push({
        timestamp: Date.now(),
        ...data
      });

      // Keep only last 100 entries to prevent localStorage bloat
      if (existingData.length > 100) {
        existingData.splice(0, existingData.length - 100);
      }

      localStorage.setItem('analytics_data', JSON.stringify(existingData));
    } catch (error) {
      console.warn('Failed to store analytics data:', error);
    }
  }

  public getSessionData(): UserSession {
    return { ...this.session };
  }

  public getAnalyticsData(): any[] {
    try {
      return JSON.parse(localStorage.getItem('analytics_data') || '[]');
    } catch {
      return [];
    }
  }

  public clearAnalyticsData(): void {
    localStorage.removeItem('analytics_data');
  }

  public disable(): void {
    this.isEnabled = false;
  }

  public enable(): void {
    this.isEnabled = true;
  }

  public isAnalyticsEnabled(): boolean {
    return this.isEnabled;
  }
}

// Create singleton instance
export const analytics = new AnalyticsManager();

// Convenience functions
export const trackEvent = (eventData: Partial<AnalyticsEvent>) => analytics.trackEvent(eventData);
export const reportError = (errorData: Partial<ErrorReport>) => analytics.reportError(errorData);
export const trackPageView = (path?: string) => analytics.trackPageView(path);
export const trackUserAction = (action: string, category?: string, label?: string, value?: number) =>
  analytics.trackUserAction(action, category, label, value);
export const trackGameEvent = (action: string, label?: string, value?: number) =>
  analytics.trackGameEvent(action, label, value);
export const trackCustomization = (action: string, carModel: string, customizationType: string) =>
  analytics.trackCustomization(action, carModel, customizationType);
export const trackAchievement = (achievementId: string, achievementName: string) =>
  analytics.trackAchievement(achievementId, achievementName);
export const trackEventRegistration = (eventId: string, eventType: string) =>
  analytics.trackEventRegistration(eventId, eventType);



export default analytics;
