
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";

interface Event {
  id: number;
  title: string;
  type: string;
  date: string;
  time: string;
  location: string;
  description: string;
  image: string;
  featured?: boolean;
}

const EventsPreview = () => {
  const events: Event[] = [
    {
      id: 1,
      title: "Summernats Burnout Championship",
      type: "Burnout",
      date: "June 15, 2025",
      time: "12:00 PM",
      location: "Sydney Motorsport Park",
      description: "Australia's premier burnout competition with the biggest prize pool in the country. Show off your skills and become a legend!",
      image: "/lovable-uploads/9e58dd2b-6e86-49da-bf47-452803c2f47c.png",
      featured: true
    },
    {
      id: 2,
      title: "Quarter-Mile Showdown",
      type: "Drag Racing",
      date: "July 2, 2025",
      time: "7:00 PM",
      location: "Willowbank Raceway",
      description: "Test your quarter-mile speed in this high-octane drag racing event. Multiple classes available for all types of vehicles.",
      image: "/lovable-uploads/d926514d-0e22-447a-a4f4-c174e60d75a1.png"
    },
    {
      id: 3,
      title: "Ute Muster Grand Prix",
      type: "Off-road",
      date: "August 10, 2025",
      time: "10:00 AM",
      location: "Deniliquin Paddocks",
      description: "The ultimate off-road experience for ute lovers. Mud, dirt, and high-flying action guaranteed!",
      image: "/lovable-uploads/9ae699d3-8205-4b18-8268-912dc2262eb8.png"
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-b from-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-racing-yellow mb-4">Upcoming Events</h2>
          <p className="text-gray-300 max-w-3xl mx-auto">
            Compete in Australia's most intense motorsport events. From burnouts to drag races, 
            show off your skills and earn your place in Aussie Burnout Legends history.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {events.map((event) => (
            <Card key={event.id} className={`overflow-hidden bg-black/40 border-racing-yellow/20 h-full flex flex-col ${event.featured ? 'md:col-span-2 lg:col-span-1 ring-2 ring-racing-yellow' : ''}`}>
              <div className="relative h-48 overflow-hidden">
                <img
                  src={event.image}
                  alt={event.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>
                <div className="absolute bottom-4 left-4">
                  <Badge className="bg-racing-red text-white border-none">
                    {event.type}
                  </Badge>
                  {event.featured && (
                    <Badge className="ml-2 bg-racing-yellow text-black border-none">
                      Featured
                    </Badge>
                  )}
                </div>
              </div>
              
              <CardHeader>
                <CardTitle className="text-racing-yellow">{event.title}</CardTitle>
                <CardDescription className="text-gray-400">
                  {event.date} at {event.time} • {event.location}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="flex-grow">
                <p className="text-gray-300">{event.description}</p>
              </CardContent>
              
              <CardFooter>
                <Button className="w-full bg-racing-red hover:bg-red-700 text-white">
                  View Details
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <Button variant="outline" className="border-racing-yellow text-racing-yellow hover:bg-racing-yellow hover:text-black">
            View All Events
          </Button>
        </div>
      </div>
    </section>
  );
};

export default EventsPreview;
