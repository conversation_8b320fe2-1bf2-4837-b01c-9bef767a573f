import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { X, ArrowRight, ArrowLeft, Play, Trophy, Car, Settings, Calendar } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import AustralianEmblem from '@/components/AustralianEmblem';

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  target?: string; // CSS selector for highlighting
  action?: 'click' | 'hover' | 'scroll' | 'wait';
  content: React.ReactNode;
  skippable?: boolean;
}

const TUTORIAL_STEPS: TutorialStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Aussie Burnout Legends!',
    description: 'Let\'s get you started with the ultimate Australian car culture experience',
    icon: <AustralianEmblem size="sm" />,
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          G'day mate! Welcome to Aussie Burnout Legends, where you can experience the thrill 
          of Australian car culture like never before.
        </p>
        <div className="bg-racing-yellow/10 border border-racing-yellow/20 rounded-lg p-4">
          <h4 className="text-racing-yellow font-semibold mb-2">What you'll learn:</h4>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• How to customize your cars</li>
            <li>• Playing the driving simulator</li>
            <li>• Unlocking achievements</li>
            <li>• Joining events</li>
          </ul>
        </div>
      </div>
    ),
    skippable: true
  },
  {
    id: 'navigation',
    title: 'Navigation Basics',
    description: 'Learn how to navigate around the platform',
    icon: <Car className="w-6 h-6" />,
    target: 'nav',
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          Use the navigation bar to access different sections:
        </p>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="bg-gray-800 p-3 rounded">
            <strong className="text-white">Home:</strong> Main dashboard
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <strong className="text-white">Play Game:</strong> Driving simulator
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <strong className="text-white">Garage:</strong> Car collection
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <strong className="text-white">Events:</strong> Competitions
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'garage',
    title: 'Your Garage',
    description: 'Manage and customize your car collection',
    icon: <Settings className="w-6 h-6" />,
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          Your garage is where you can:
        </p>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-racing-yellow rounded-full"></div>
            <span className="text-gray-300">View your car collection</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-racing-yellow rounded-full"></div>
            <span className="text-gray-300">Customize paint, wheels, and performance</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-racing-yellow rounded-full"></div>
            <span className="text-gray-300">Purchase new cars</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-racing-yellow rounded-full"></div>
            <span className="text-gray-300">Track your achievements</span>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'game',
    title: 'Driving Simulator',
    description: 'Master the art of burnouts and drifts',
    icon: <Play className="w-6 h-6" />,
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          The driving game features realistic physics and scoring:
        </p>
        <div className="bg-gray-800 p-4 rounded-lg">
          <h4 className="text-white font-semibold mb-2">Controls:</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div><kbd className="bg-gray-700 px-2 py-1 rounded">W/↑</kbd> Throttle</div>
            <div><kbd className="bg-gray-700 px-2 py-1 rounded">S/↓</kbd> Brake</div>
            <div><kbd className="bg-gray-700 px-2 py-1 rounded">A/←</kbd> Steer Left</div>
            <div><kbd className="bg-gray-700 px-2 py-1 rounded">D/→</kbd> Steer Right</div>
            <div className="col-span-2"><kbd className="bg-gray-700 px-2 py-1 rounded">Space</kbd> Handbrake</div>
          </div>
        </div>
        <div className="text-sm text-gray-400">
          <strong>Tip:</strong> Hold throttle + handbrake while stationary for burnouts!
        </div>
      </div>
    )
  },
  {
    id: 'achievements',
    title: 'Achievement System',
    description: 'Unlock rewards and track your progress',
    icon: <Trophy className="w-6 h-6" />,
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          Earn achievements by completing various challenges:
        </p>
        <div className="space-y-3">
          <div className="flex items-center gap-3 bg-gray-800 p-3 rounded">
            <div className="w-8 h-8 bg-racing-yellow rounded-full flex items-center justify-center">
              🔥
            </div>
            <div>
              <div className="text-white font-medium">First Burnout</div>
              <div className="text-xs text-gray-400">Complete your first burnout challenge</div>
            </div>
          </div>
          <div className="flex items-center gap-3 bg-gray-800 p-3 rounded">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              💨
            </div>
            <div>
              <div className="text-white font-medium">Sideways Sensation</div>
              <div className="text-xs text-gray-400">Complete your first drift challenge</div>
            </div>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'events',
    title: 'Events & Competitions',
    description: 'Join exciting events and compete with others',
    icon: <Calendar className="w-6 h-6" />,
    content: (
      <div className="space-y-4">
        <p className="text-gray-300">
          Participate in various events to win prizes:
        </p>
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-red-900/20 border border-red-500/20 p-3 rounded">
            <div className="text-red-400 font-semibold">🔥 Burnout</div>
            <div className="text-xs text-gray-400">Smoke competitions</div>
          </div>
          <div className="bg-blue-900/20 border border-blue-500/20 p-3 rounded">
            <div className="text-blue-400 font-semibold">💨 Drift</div>
            <div className="text-xs text-gray-400">Sideways challenges</div>
          </div>
          <div className="bg-green-900/20 border border-green-500/20 p-3 rounded">
            <div className="text-green-400 font-semibold">🏁 Race</div>
            <div className="text-xs text-gray-400">Speed competitions</div>
          </div>
          <div className="bg-purple-900/20 border border-purple-500/20 p-3 rounded">
            <div className="text-purple-400 font-semibold">✨ Show</div>
            <div className="text-xs text-gray-400">Beauty contests</div>
          </div>
        </div>
      </div>
    )
  },
  {
    id: 'complete',
    title: 'You\'re Ready to Go!',
    description: 'Start your journey in Aussie Burnout Legends',
    icon: <Trophy className="w-6 h-6" />,
    content: (
      <div className="space-y-4 text-center">
        <div className="text-6xl">🏆</div>
        <p className="text-gray-300">
          Congratulations! You've completed the tutorial and are ready to experience 
          the best of Australian car culture.
        </p>
        <div className="bg-racing-yellow/10 border border-racing-yellow/20 rounded-lg p-4">
          <h4 className="text-racing-yellow font-semibold mb-2">What's Next?</h4>
          <div className="text-sm text-gray-300 space-y-1">
            <div>• Customize your Founders Edition RX-3</div>
            <div>• Play the driving simulator</div>
            <div>• Join your first event</div>
            <div>• Unlock achievements</div>
          </div>
        </div>
      </div>
    )
  }
];

interface InteractiveTutorialProps {
  onComplete?: () => void;
  onSkip?: () => void;
}

const InteractiveTutorial: React.FC<InteractiveTutorialProps> = ({ 
  onComplete, 
  onSkip 
}) => {
  const { user, unlockAchievement } = useAuth();
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const currentStepData = TUTORIAL_STEPS[currentStep];
  const progress = ((currentStep + 1) / TUTORIAL_STEPS.length) * 100;

  const handleNext = () => {
    if (currentStep < TUTORIAL_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    setIsVisible(false);
    
    // Unlock tutorial completion achievement
    if (user) {
      unlockAchievement('tutorial_complete');
    }
    
    toast({
      title: "Tutorial Complete!",
      description: "Welcome to Aussie Burnout Legends! Start your engines!",
      className: "bg-racing-black border-racing-yellow text-white",
    });
    
    if (onComplete) {
      onComplete();
    }
  };

  const handleSkip = () => {
    setIsVisible(false);
    
    toast({
      title: "Tutorial Skipped",
      description: "You can always access help from the menu.",
      className: "bg-racing-black border-racing-yellow text-white",
    });
    
    if (onSkip) {
      onSkip();
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-black/90 border-racing-yellow/20">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-racing-yellow/20 rounded-lg">
                {currentStepData.icon}
              </div>
              <div>
                <CardTitle className="text-racing-yellow">{currentStepData.title}</CardTitle>
                <CardDescription className="text-gray-400">
                  {currentStepData.description}
                </CardDescription>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-racing-yellow border-racing-yellow/20">
                {currentStep + 1} / {TUTORIAL_STEPS.length}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSkip}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <div className="mt-4">
            <div className="flex justify-between text-xs text-gray-400 mb-1">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="min-h-[200px]">
            {currentStepData.content}
          </div>
          
          <div className="flex items-center justify-between pt-4 border-t border-gray-700">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Previous
            </Button>
            
            <div className="flex items-center gap-2">
              {currentStepData.skippable && (
                <Button
                  variant="ghost"
                  onClick={handleSkip}
                  className="text-gray-400"
                >
                  Skip Tutorial
                </Button>
              )}
              
              <Button
                onClick={handleNext}
                className="bg-racing-red hover:bg-red-700 text-white flex items-center gap-2"
              >
                {currentStep === TUTORIAL_STEPS.length - 1 ? 'Get Started' : 'Next'}
                <ArrowRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default InteractiveTutorial;
