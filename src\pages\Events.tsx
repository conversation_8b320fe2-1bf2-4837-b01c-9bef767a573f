
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Card, CardContent, CardD<PERSON><PERSON>, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

interface Event {
  id: number;
  title: string;
  type: string;
  date: string;
  time: string;
  location: string;
  description: string;
  image: string;
  participants?: number;
  maxParticipants?: number;
  prizes?: string[];
  featured?: boolean;
}

const Events = () => {
  const events: Record<string, Event[]> = {
    upcoming: [
      {
        id: 1,
        title: "Summernats Burnout Championship",
        type: "Burnout",
        date: "June 15, 2025",
        time: "12:00 PM",
        location: "Sydney Motorsport Park",
        description: "Australia's premier burnout competition with the biggest prize pool in the country. Show off your skills and become a legend!",
        image: "/lovable-uploads/9e58dd2b-6e86-49da-bf47-452803c2f47c.png",
        participants: 27,
        maxParticipants: 50,
        prizes: ["$10,000 Cash", "Championship Trophy", "Exclusive In-Game Items"],
        featured: true
      },
      {
        id: 2,
        title: "Quarter-Mile Showdown",
        type: "Drag Racing",
        date: "July 2, 2025",
        time: "7:00 PM",
        location: "Willowbank Raceway",
        description: "Test your quarter-mile speed in this high-octane drag racing event. Multiple classes available for all types of vehicles.",
        image: "/lovable-uploads/d926514d-0e22-447a-a4f4-c174e60d75a1.png",
        participants: 42,
        maxParticipants: 64,
        prizes: ["$5,000 Cash", "Performance Parts", "Trophy"]
      },
      {
        id: 3,
        title: "Ute Muster Grand Prix",
        type: "Off-road",
        date: "August 10, 2025",
        time: "10:00 AM",
        location: "Deniliquin Paddocks",
        description: "The ultimate off-road experience for ute lovers. Mud, dirt, and high-flying action guaranteed!",
        image: "/lovable-uploads/9ae699d3-8205-4b18-8268-912dc2262eb8.png",
        participants: 15,
        maxParticipants: 30,
        prizes: ["Custom Ute Trophy", "$3,000 Cash"]
      },
      {
        id: 4,
        title: "Founder's Day Exhibition",
        type: "Exhibition",
        date: "September 5, 2025",
        time: "3:00 PM",
        location: "Melbourne Exhibition Centre",
        description: "Special event exclusively for owners of the Founders Edition Mazda RX-3. Show off your unique car and win special prizes!",
        image: "/lovable-uploads/20adddf2-9fe4-455f-8e10-8f4773d69892.png",
        participants: 8,
        maxParticipants: 100,
        prizes: ["Exclusive Founders Badge", "Limited Edition Merchandise"],
        featured: true
      }
    ],
    past: [
      {
        id: 101,
        title: "Autumn Drag Challenge",
        type: "Drag Racing",
        date: "April 22, 2025",
        time: "6:00 PM",
        location: "Calder Park Raceway",
        description: "Season opener for the drag racing championship. Multiple competitors battled it out for early season points.",
        image: "/lovable-uploads/a594cad8-350e-482b-b30c-73b539a00109.png"
      },
      {
        id: 102,
        title: "Rotary Revival",
        type: "Exhibition",
        date: "March 15, 2025",
        time: "11:00 AM",
        location: "Eastern Creek",
        description: "A celebration of rotary-powered vehicles with display areas, sound competitions and dyno testing.",
        image: "/lovable-uploads/25f1a61c-a8fe-49cf-9948-4322a8be4884.png"
      }
    ]
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow py-16 bg-gradient-to-b from-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-racing-yellow mb-4">Events Calendar</h1>
            <p className="text-gray-300">
              Compete in Australia's most intense motorsport events, from burnouts to drag races, and earn your place in Aussie Burnout Legends history.
            </p>
          </div>
          
          <Tabs defaultValue="upcoming" className="w-full">
            <TabsList className="w-full grid grid-cols-3 max-w-md mb-8">
              <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
              <TabsTrigger value="past">Past Events</TabsTrigger>
              <TabsTrigger value="your-events">Your Events</TabsTrigger>
            </TabsList>
            
            <TabsContent value="upcoming">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {events.upcoming.filter(event => event.featured).map((event) => (
                  <Card key={event.id} className="overflow-hidden bg-black/40 border-racing-yellow/20 lg:col-span-2">
                    <div className="grid grid-cols-1 lg:grid-cols-3">
                      <div className="relative h-60 lg:h-full">
                        <img src={event.image} alt={event.title} className="h-full w-full object-cover" />
                        <div className="absolute top-4 left-4 flex gap-2">
                          <Badge className="bg-racing-red text-white border-none">
                            {event.type}
                          </Badge>
                          <Badge className="bg-racing-yellow text-black border-none">
                            Featured
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="lg:col-span-2 p-6">
                        <h3 className="text-2xl font-bold text-racing-yellow mb-2">{event.title}</h3>
                        <div className="flex flex-wrap gap-y-2 gap-x-4 mb-4 text-sm">
                          <div className="flex items-center text-gray-300">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                            </svg>
                            {event.date}
                          </div>
                          <div className="flex items-center text-gray-300">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            {event.time}
                          </div>
                          <div className="flex items-center text-gray-300">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                            </svg>
                            {event.location}
                          </div>
                        </div>
                        
                        <p className="text-gray-300 mb-6">{event.description}</p>
                        
                        {event.prizes && (
                          <div className="mb-6">
                            <h4 className="text-sm font-semibold text-racing-yellow mb-2">Prizes:</h4>
                            <ul className="flex flex-wrap gap-2">
                              {event.prizes.map((prize, index) => (
                                <li key={index} className="bg-black/30 px-2 py-1 rounded text-sm text-gray-300">
                                  {prize}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-sm text-gray-400 mb-1">Participants</div>
                            <div className="w-40 bg-gray-700 rounded-full h-2 mb-1">
                              <div
                                className="bg-racing-green h-2 rounded-full"
                                style={{ width: `${(event.participants! / event.maxParticipants!) * 100}%` }}
                              ></div>
                            </div>
                            <div className="text-xs text-gray-400">
                              {event.participants}/{event.maxParticipants} registered
                            </div>
                          </div>
                          <Button className="bg-racing-red hover:bg-red-700 text-white">
                            Register Now
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:col-span-2">
                  {events.upcoming.filter(event => !event.featured).map((event) => (
                    <Card key={event.id} className="overflow-hidden bg-black/40 border-racing-yellow/20 flex flex-col">
                      <div className="relative h-48">
                        <img src={event.image} alt={event.title} className="h-full w-full object-cover" />
                        <div className="absolute top-4 left-4">
                          <Badge className="bg-racing-red text-white border-none">
                            {event.type}
                          </Badge>
                        </div>
                      </div>
                      
                      <CardHeader>
                        <CardTitle className="text-racing-yellow">{event.title}</CardTitle>
                        <CardDescription className="text-gray-400">
                          {event.date} • {event.time}
                        </CardDescription>
                      </CardHeader>
                      
                      <CardContent className="flex-grow">
                        <p className="text-gray-300 mb-4">{event.description}</p>
                        <div className="text-sm text-gray-400 mb-1">Participants</div>
                        <div className="w-full bg-gray-700 rounded-full h-2 mb-1">
                          <div
                            className="bg-racing-green h-2 rounded-full"
                            style={{ width: `${(event.participants! / event.maxParticipants!) * 100}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-400">
                          {event.participants}/{event.maxParticipants} registered
                        </div>
                      </CardContent>
                      
                      <CardFooter>
                        <Button className="w-full bg-racing-red hover:bg-red-700 text-white">
                          Register
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="past">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {events.past.map((event) => (
                  <Card key={event.id} className="overflow-hidden bg-black/40 border-racing-yellow/20 flex flex-col">
                    <div className="relative h-48">
                      <img src={event.image} alt={event.title} className="h-full w-full object-cover filter grayscale opacity-80" />
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-gray-600 text-white border-none">
                          {event.type}
                        </Badge>
                      </div>
                      <div className="absolute top-0 right-0 bg-black/70 px-3 py-1">
                        <span className="text-gray-400 text-sm">Completed</span>
                      </div>
                    </div>
                    
                    <CardHeader>
                      <CardTitle className="text-gray-400">{event.title}</CardTitle>
                      <CardDescription className="text-gray-500">
                        {event.date} • {event.time}
                      </CardDescription>
                    </CardHeader>
                    
                    <CardContent className="flex-grow">
                      <p className="text-gray-400">{event.description}</p>
                    </CardContent>
                    
                    <CardFooter>
                      <Button variant="outline" className="w-full text-gray-400 border-gray-600">
                        View Results
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="your-events">
              <div className="bg-black/40 border border-racing-yellow/20 rounded-lg p-8 text-center">
                <div className="flex flex-col items-center justify-center py-12 space-y-4">
                  <div className="h-20 w-20 rounded-full bg-gray-800 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                      <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                      <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-racing-yellow">No Events Registered</h3>
                  <p className="text-gray-400 max-w-lg">
                    You haven't registered for any events yet. Browse the upcoming events and register 
                    to compete against other players!
                  </p>
                  <Button className="mt-4 bg-racing-red hover:bg-red-700 text-white">
                    Browse Events
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Events;
