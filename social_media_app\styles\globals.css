@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Inter', sans-serif;
  background: #F9FAFB;
  color: #1F2937;
  min-height: 100vh;
}

@layer components {
  .btn-primary {
    @apply bg-primary text-white rounded-md px-5 py-2 font-semibold transition-all duration-200 ease-out shadow hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-accent;
  }
  .card {
    @apply bg-white rounded-2xl shadow-md transition-all duration-200 ease-out hover:scale-105 hover:shadow-lg;
  }
  .input {
    @apply border border-gray-200 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200;
  }
  .glass {
    background: rgba(255,255,255,0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  .sticky-nav {
    @apply sticky top-0 z-30 glass shadow;
  }
  .feed-bg {
    background: linear-gradient(135deg, #F0F9FF 0%, #E1F2FE 100%);
  }
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded-md;
  }
}
proceed all just do it u know what to do