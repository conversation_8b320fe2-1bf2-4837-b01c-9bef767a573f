import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Trophy,
  Star,
  Crown,
  Flame,
  Wind,
  Zap,
  Sparkles,
  ChevronRight,
  Heart,
  Share2
} from 'lucide-react';
import { placeholderImages, eventImages } from '@/assets/images/cars';
import { EventImage } from '@/components/graphics/EnhancedImageLoader';
import { useGraphics } from '@/components/graphics/GraphicsOptimizer';

interface EventPrizes {
  first: string;
  second: string;
  third: string;
}

interface EventRequirements {
  minLevel?: number;
  carType?: string;
  foundersOnly?: boolean;
}

interface EnhancedEventCardProps {
  id: string;
  title: string;
  description: string;
  type: 'burnout' | 'drift' | 'race' | 'show';
  date: string;
  time: string;
  location: string;
  maxParticipants: number;
  currentParticipants: number;
  entryFee: number;
  prizes: EventPrizes;
  requirements: EventRequirements;
  status: 'upcoming' | 'registration' | 'ongoing' | 'completed';
  registered: boolean;
  featured?: boolean;
  onRegister?: () => void;
  onUnregister?: () => void;
  onViewDetails?: () => void;
  className?: string;
}

const EnhancedEventCard: React.FC<EnhancedEventCardProps> = ({
  id,
  title,
  description,
  type,
  date,
  time,
  location,
  maxParticipants,
  currentParticipants,
  entryFee,
  prizes,
  requirements,
  status,
  registered,
  featured = false,
  onRegister,
  onUnregister,
  onViewDetails,
  className
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showPrizes, setShowPrizes] = useState(false);
  const { shouldRenderEffect, getOptimizedImageUrl } = useGraphics();

  const getEventIcon = () => {
    switch (type) {
      case 'burnout': return <Flame className="w-6 h-6" />;
      case 'drift': return <Wind className="w-6 h-6" />;
      case 'race': return <Zap className="w-6 h-6" />;
      case 'show': return <Sparkles className="w-6 h-6" />;
      default: return <Trophy className="w-6 h-6" />;
    }
  };

  const getEventColor = () => {
    switch (type) {
      case 'burnout': return {
        bg: 'from-red-500 to-orange-600',
        border: 'border-red-500/30',
        text: 'text-red-400',
        glow: 'shadow-red-500/20'
      };
      case 'drift': return {
        bg: 'from-blue-500 to-cyan-600',
        border: 'border-blue-500/30',
        text: 'text-blue-400',
        glow: 'shadow-blue-500/20'
      };
      case 'race': return {
        bg: 'from-green-500 to-emerald-600',
        border: 'border-green-500/30',
        text: 'text-green-400',
        glow: 'shadow-green-500/20'
      };
      case 'show': return {
        bg: 'from-purple-500 to-pink-600',
        border: 'border-purple-500/30',
        text: 'text-purple-400',
        glow: 'shadow-purple-500/20'
      };
      default: return {
        bg: 'from-gray-500 to-gray-600',
        border: 'border-gray-500/30',
        text: 'text-gray-400',
        glow: 'shadow-gray-500/20'
      };
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'upcoming': return 'bg-blue-500';
      case 'registration': return 'bg-green-500';
      case 'ongoing': return 'bg-yellow-500';
      case 'completed': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const participationPercentage = (currentParticipants / maxParticipants) * 100;
  const eventColors = getEventColor();

  // Get the appropriate event image
  const getEventImage = () => {
    switch (type) {
      case 'burnout': return eventImages.burnout.championship;
      case 'drift': return eventImages.drift.series;
      case 'race': return eventImages.race.circuit;
      case 'show': return eventImages.show.display;
      default: return eventImages.burnout.championship;
    }
  };

  const eventImage = getEventImage();

  return (
    <motion.div
      className={`relative ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className={`
        relative overflow-hidden bg-gradient-to-br from-gray-900/90 to-black/90
        backdrop-blur-sm border-2 ${eventColors.border}
        ${registered ? 'ring-2 ring-racing-yellow/50' : ''}
        ${isHovered ? `shadow-2xl ${eventColors.glow}` : 'shadow-lg'}
        transition-all duration-300
        ${featured ? 'lg:col-span-2' : ''}
      `}>
        {/* Featured Glow Effect */}
        {featured && (
          <div className={`absolute inset-0 bg-gradient-to-r ${eventColors.bg} opacity-10 animate-pulse`} />
        )}

        {/* Image Section */}
        <div className={`relative ${featured ? 'h-64' : 'h-48'} overflow-hidden`}>
          <EventImage
            src={getOptimizedImageUrl(eventImage, featured ? 800 : 600, featured ? 400 : 300)}
            alt={title}
            eventType={type}
            width={featured ? 800 : 600}
            height={featured ? 400 : 300}
            quality="high"
            className={`w-full h-full object-cover transition-all duration-500 ${
              shouldRenderEffect('animations') && isHovered ? 'scale-110' : 'scale-100'
            }`}
            overlay={true}
            overlayColor="rgba(0, 0, 0, 0.2)"
          />

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/40" />

          {/* Top Badges */}
          <div className="absolute top-4 left-4 flex flex-wrap gap-2">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring" }}
            >
              <Badge className={`bg-gradient-to-r ${eventColors.bg} text-white font-bold px-3 py-1`}>
                {getEventIcon()}
                <span className="ml-2 capitalize">{type}</span>
              </Badge>
            </motion.div>

            {featured && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3, type: "spring" }}
              >
                <Badge className="bg-gradient-to-r from-racing-yellow to-orange-500 text-black font-bold px-3 py-1">
                  <Star className="w-3 h-3 mr-1" />
                  Featured
                </Badge>
              </motion.div>
            )}

            {requirements.foundersOnly && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.4, type: "spring" }}
              >
                <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold px-3 py-1">
                  <Crown className="w-3 h-3 mr-1" />
                  Founders Only
                </Badge>
              </motion.div>
            )}

            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.5, type: "spring" }}
            >
              <Badge className={`${getStatusColor()} text-white capitalize px-2 py-1`}>
                {status}
              </Badge>
            </motion.div>
          </div>

          {/* Top Right Actions */}
          <div className="absolute top-4 right-4 flex gap-2">
            <motion.button
              className="p-2 bg-black/50 backdrop-blur-sm rounded-full text-white hover:bg-racing-yellow hover:text-black transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Heart className="w-4 h-4" />
            </motion.button>

            <motion.button
              className="p-2 bg-black/50 backdrop-blur-sm rounded-full text-white hover:bg-racing-yellow hover:text-black transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Share2 className="w-4 h-4" />
            </motion.button>
          </div>

          {/* Registration Status */}
          {registered && (
            <motion.div
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
              initial={{ scale: 0, rotate: -10 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: 0.6, type: "spring" }}
            >
              <div className="bg-racing-yellow text-black font-bold px-4 py-2 rounded-full border-2 border-white shadow-lg">
                ✓ REGISTERED
              </div>
            </motion.div>
          )}

          {/* Bottom Event Info */}
          <div className="absolute bottom-4 left-4 right-4">
            <h3 className="text-white font-bold text-xl mb-2">{title}</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center text-gray-300">
                <Calendar className="w-4 h-4 mr-2" />
                {new Date(date).toLocaleDateString()}
              </div>
              <div className="flex items-center text-gray-300">
                <Clock className="w-4 h-4 mr-2" />
                {time}
              </div>
              <div className="flex items-center text-gray-300 col-span-2">
                <MapPin className="w-4 h-4 mr-2" />
                {location}
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <CardContent className="p-6 space-y-4">
          <p className="text-gray-300 text-sm leading-relaxed">{description}</p>

          {/* Participation Progress */}
          <div className="space-y-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-400 flex items-center">
                <Users className="w-4 h-4 mr-2" />
                Participants
              </span>
              <span className="text-white font-semibold">
                {currentParticipants}/{maxParticipants}
              </span>
            </div>
            <div className="relative">
              <Progress value={participationPercentage} className="h-3" />
              <motion.div
                className={`absolute inset-0 bg-gradient-to-r ${eventColors.bg} opacity-30 rounded-full`}
                initial={{ width: 0 }}
                animate={{ width: `${participationPercentage}%` }}
                transition={{ duration: 1, delay: 0.5 }}
              />
            </div>
            <div className="text-xs text-gray-500 text-center">
              {Math.round(participationPercentage)}% full
            </div>
          </div>

          {/* Entry Fee */}
          <div className="flex justify-between items-center">
            <span className="text-gray-400">Entry Fee:</span>
            <span className={`font-bold ${entryFee === 0 ? 'text-green-400' : 'text-racing-yellow'}`}>
              {entryFee === 0 ? 'FREE' : `$${entryFee}`}
            </span>
          </div>

          {/* Prizes Section */}
          <div className="space-y-2">
            <button
              onClick={() => setShowPrizes(!showPrizes)}
              className="flex items-center justify-between w-full text-left"
            >
              <span className="text-gray-400 flex items-center">
                <Trophy className="w-4 h-4 mr-2" />
                Prizes
              </span>
              <motion.div
                animate={{ rotate: showPrizes ? 90 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </motion.div>
            </button>

            <AnimatePresence>
              {showPrizes && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-2 overflow-hidden"
                >
                  <div className="grid grid-cols-1 gap-2 text-xs">
                    <div className="flex items-center justify-between p-2 bg-yellow-500/10 rounded border border-yellow-500/20">
                      <span className="flex items-center text-yellow-400">
                        <Star className="w-3 h-3 mr-1" />
                        1st Place
                      </span>
                      <span className="text-white">{prizes.first}</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-500/10 rounded border border-gray-500/20">
                      <span className="flex items-center text-gray-400">
                        <Star className="w-3 h-3 mr-1" />
                        2nd Place
                      </span>
                      <span className="text-white">{prizes.second}</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-orange-500/10 rounded border border-orange-500/20">
                      <span className="flex items-center text-orange-400">
                        <Star className="w-3 h-3 mr-1" />
                        3rd Place
                      </span>
                      <span className="text-white">{prizes.third}</span>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            {registered ? (
              <Button
                onClick={onUnregister}
                variant="outline"
                className="flex-1 border-red-500/50 text-red-400 hover:bg-red-500/10"
              >
                Unregister
              </Button>
            ) : (
              <Button
                onClick={onRegister}
                className={`flex-1 bg-gradient-to-r ${eventColors.bg} hover:opacity-90 text-white font-semibold`}
                disabled={currentParticipants >= maxParticipants}
              >
                {currentParticipants >= maxParticipants ? 'Event Full' : 'Register Now'}
              </Button>
            )}

            <Button
              onClick={onViewDetails}
              variant="outline"
              className="px-6"
            >
              Details
            </Button>
          </div>
        </CardContent>

        {/* Hover Glow Effect */}
        <AnimatePresence>
          {isHovered && (
            <motion.div
              className={`absolute inset-0 bg-gradient-to-r ${eventColors.bg} opacity-10 pointer-events-none`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            />
          )}
        </AnimatePresence>
      </Card>
    </motion.div>
  );
};

export default EnhancedEventCard;
