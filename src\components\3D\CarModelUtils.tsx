
import React from 'react';
import { MeshStandardMaterial, Vector3, BufferGeometry, Object3D } from 'three';

// Reusable components to build better car models
export const createCarMaterial = (
  color: string,
  metalness: number = 0.5,
  roughness: number = 0.3
) => {
  return new MeshStandardMaterial({
    color,
    metalness,
    roughness,
  });
};

// Create a helper for positioning wheels
export const createWheelPosition = (
  axlePosition: number,
  side: 'left' | 'right',
  wheelBase: number,
  trackWidth: number,
  height: number = 0
): Vector3 => {
  const sideMultiplier = side === 'left' ? 1 : -1;
  return new Vector3(
    axlePosition,
    height,
    (trackWidth / 2) * sideMultiplier
  );
};

// RX-3 specific measurement constants - improved with more accurate measurements
export const RX3_DIMENSIONS = {
  // Body
  bodyLength: 4.2,
  bodyWidth: 1.8,
  bodyHeight: 0.8,
  
  // Cabin - adjusted for more accurate RX-3 coupe shape
  cabinLength: 2.6,
  cabinWidth: 1.7,
  cabinHeight: 0.85,
  cabinPosition: -0.1, // Slight offset from center
  
  // Hood - longer for the RX-3's distinctive front
  hoodLength: 1.6,
  hoodHeight: 0.2,
  hoodWidth: 1.75,
  
  // Trunk - added for better proportion
  trunkLength: 0.8,
  trunkHeight: 0.2,
  trunkWidth: 1.7,
  
  // Wheel - adjusted for more realistic proportions
  wheelDiameter: 0.4,
  wheelWidth: 0.2,
  
  // Positioning
  wheelBase: 2.8, // Distance between front and rear wheels
  trackWidth: 1.8,  // Distance between left and right wheels
  frontWheelPos: 1.4, // X position of front wheels from center
  rearWheelPos: -1.4, // X position of rear wheels from center
  wheelYPos: -0.2, // Y position of wheels
  
  // Detailed parts
  headlightDiameter: 0.25,
  headlightDepth: 0.1,
  headlightPositionZ: 0.6, // Distance from center
  
  taillightWidth: 0.3,
  taillightHeight: 0.2,
  taillightDepth: 0.05,
  taillightPositionZ: 0.6, // Distance from center
  
  grillWidth: 1.0,
  grillHeight: 0.3,
  grillDepth: 0.05,
  
  // Roof styling for the distinctive RX-3 coupe look
  roofSlope: 0.2, // Angle for the fastback style
};

// Australian flag colors
export const AUSTRALIA_COLORS = {
  blue: '#00008B', // Navy blue
  red: '#FF0000',  // Red
  white: '#FFFFFF', // White
};

// Special RX-3 specific materials
export const RX3_MATERIALS = {
  body: createCarMaterial(AUSTRALIA_COLORS.red, 0.8, 0.2),
  glass: createCarMaterial(AUSTRALIA_COLORS.blue, 0.9, 0.1),
  chrome: createCarMaterial(AUSTRALIA_COLORS.white, 0.9, 0.1),
  wheels: createCarMaterial('#1A1A1A', 0.4, 0.8),
  lights: createCarMaterial('#FFFFAA', 0.9, 0.1),
  taillights: createCarMaterial('#8B0000', 0.5, 0.2),
};

// Create a position helper for the RX-3
export const createRX3Position = (
  part: 'body' | 'cabin' | 'hood' | 'trunk' | 'headlight-left' | 'headlight-right' | 
        'taillight-left' | 'taillight-right' | 'wheel-front-left' | 'wheel-front-right' | 
        'wheel-rear-left' | 'wheel-rear-right' | 'grill'
): Vector3 => {
  const { frontWheelPos, rearWheelPos, wheelYPos, headlightPositionZ, taillightPositionZ } = RX3_DIMENSIONS;
  
  switch (part) {
    case 'body':
      return new Vector3(0, 0, 0);
    case 'cabin':
      return new Vector3(RX3_DIMENSIONS.cabinPosition, RX3_DIMENSIONS.bodyHeight / 2 + RX3_DIMENSIONS.cabinHeight / 2, 0);
    case 'hood':
      return new Vector3(frontWheelPos - RX3_DIMENSIONS.hoodLength / 4, RX3_DIMENSIONS.bodyHeight / 2 + RX3_DIMENSIONS.hoodHeight / 2, 0);
    case 'trunk':
      return new Vector3(rearWheelPos - RX3_DIMENSIONS.trunkLength / 4, RX3_DIMENSIONS.bodyHeight / 2 + RX3_DIMENSIONS.trunkHeight / 2, 0);
    case 'headlight-left':
      return new Vector3(frontWheelPos + RX3_DIMENSIONS.headlightDiameter, RX3_DIMENSIONS.bodyHeight / 2, headlightPositionZ);
    case 'headlight-right':
      return new Vector3(frontWheelPos + RX3_DIMENSIONS.headlightDiameter, RX3_DIMENSIONS.bodyHeight / 2, -headlightPositionZ);
    case 'taillight-left':
      return new Vector3(rearWheelPos - RX3_DIMENSIONS.taillightWidth / 2, RX3_DIMENSIONS.bodyHeight / 2, taillightPositionZ);
    case 'taillight-right':
      return new Vector3(rearWheelPos - RX3_DIMENSIONS.taillightWidth / 2, RX3_DIMENSIONS.bodyHeight / 2, -taillightPositionZ);
    case 'wheel-front-left':
      return createWheelPosition(frontWheelPos, 'left', RX3_DIMENSIONS.wheelBase, RX3_DIMENSIONS.trackWidth, wheelYPos);
    case 'wheel-front-right':
      return createWheelPosition(frontWheelPos, 'right', RX3_DIMENSIONS.wheelBase, RX3_DIMENSIONS.trackWidth, wheelYPos);
    case 'wheel-rear-left':
      return createWheelPosition(rearWheelPos, 'left', RX3_DIMENSIONS.wheelBase, RX3_DIMENSIONS.trackWidth, wheelYPos);
    case 'wheel-rear-right':
      return createWheelPosition(rearWheelPos, 'right', RX3_DIMENSIONS.wheelBase, RX3_DIMENSIONS.trackWidth, wheelYPos);
    case 'grill':
      return new Vector3(frontWheelPos + RX3_DIMENSIONS.headlightDiameter * 1.2, RX3_DIMENSIONS.bodyHeight / 2, 0);
    default:
      return new Vector3(0, 0, 0);
  }
};

// Helper function to add Australian flag emblem to a mesh
export const createAustralianEmblem = (parent: Object3D, scale: number = 1, position?: Vector3): Object3D => {
  const emblem = new Object3D();
  
  // Base blue rectangle
  const baseGeometry = new BufferGeometry();
  // ... Create base geometry
  
  // Add Southern Cross stars and Union Jack elements
  // ... Add detailed star geometry
  
  if (position) {
    emblem.position.copy(position);
  }
  
  emblem.scale.set(scale, scale, scale);
  parent.add(emblem);
  
  return emblem;
};
