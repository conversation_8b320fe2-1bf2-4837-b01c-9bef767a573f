import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Trophy, 
  Star, 
  Crown, 
  Flame, 
  Wind, 
  Zap, 
  Heart,
  Target,
  Award,
  Medal,
  Sparkles,
  Lock
} from 'lucide-react';

interface EnhancedAchievementBadgeProps {
  id: string;
  name: string;
  description: string;
  category: 'driving' | 'customization' | 'social' | 'special';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  unlocked: boolean;
  progress?: number;
  maxProgress?: number;
  unlockedAt?: Date;
  reward?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showDetails?: boolean;
  animated?: boolean;
  onClick?: () => void;
  className?: string;
}

const EnhancedAchievementBadge: React.FC<EnhancedAchievementBadgeProps> = ({
  id,
  name,
  description,
  category,
  rarity,
  unlocked,
  progress = 0,
  maxProgress = 100,
  unlockedAt,
  reward,
  size = 'md',
  showDetails = false,
  animated = true,
  onClick,
  className
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  const getAchievementIcon = () => {
    switch (id) {
      case 'welcome': return <Heart className="w-full h-full" />;
      case 'first_burnout': return <Flame className="w-full h-full" />;
      case 'first_drift': return <Wind className="w-full h-full" />;
      case 'speed_demon': return <Zap className="w-full h-full" />;
      case 'style_master': return <Sparkles className="w-full h-full" />;
      case 'perfectionist': return <Target className="w-full h-full" />;
      case 'legend': return <Crown className="w-full h-full" />;
      default: return <Trophy className="w-full h-full" />;
    }
  };

  const getRarityColors = () => {
    switch (rarity) {
      case 'common': return {
        bg: 'from-gray-400 to-gray-600',
        border: 'border-gray-400',
        glow: 'shadow-gray-400/50',
        text: 'text-gray-300'
      };
      case 'rare': return {
        bg: 'from-blue-400 to-blue-600',
        border: 'border-blue-400',
        glow: 'shadow-blue-400/50',
        text: 'text-blue-300'
      };
      case 'epic': return {
        bg: 'from-purple-400 to-purple-600',
        border: 'border-purple-400',
        glow: 'shadow-purple-400/50',
        text: 'text-purple-300'
      };
      case 'legendary': return {
        bg: 'from-yellow-400 via-orange-500 to-red-500',
        border: 'border-yellow-400',
        glow: 'shadow-yellow-400/50',
        text: 'text-yellow-300'
      };
      default: return {
        bg: 'from-gray-400 to-gray-600',
        border: 'border-gray-400',
        glow: 'shadow-gray-400/50',
        text: 'text-gray-300'
      };
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'w-12 h-12';
      case 'md': return 'w-16 h-16';
      case 'lg': return 'w-20 h-20';
      case 'xl': return 'w-24 h-24';
      default: return 'w-16 h-16';
    }
  };

  const colors = getRarityColors();
  const sizeClasses = getSizeClasses();
  const progressPercentage = maxProgress ? (progress / maxProgress) * 100 : 0;

  return (
    <div className={`relative ${className}`}>
      <motion.div
        className={`
          relative ${sizeClasses} cursor-pointer
          ${onClick ? 'hover:scale-110' : ''}
          transition-transform duration-300
        `}
        initial={animated ? { scale: 0, rotate: -180 } : {}}
        animate={animated ? { scale: 1, rotate: 0 } : {}}
        transition={animated ? { 
          type: "spring", 
          stiffness: 200, 
          damping: 15,
          delay: 0.2 
        } : {}}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        onClick={onClick}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        {/* Main Badge Circle */}
        <div className={`
          relative w-full h-full rounded-full border-4 ${colors.border}
          ${unlocked 
            ? `bg-gradient-to-br ${colors.bg} ${isHovered ? `shadow-2xl ${colors.glow}` : 'shadow-lg'}` 
            : 'bg-gray-800 border-gray-600'
          }
          transition-all duration-300
          flex items-center justify-center
          overflow-hidden
        `}>
          {/* Background Pattern */}
          {unlocked && rarity === 'legendary' && (
            <div className="absolute inset-0 opacity-20">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent animate-pulse" />
            </div>
          )}

          {/* Icon */}
          <div className={`
            relative z-10 p-2
            ${unlocked ? 'text-white' : 'text-gray-500'}
            ${isHovered && unlocked ? 'scale-110' : 'scale-100'}
            transition-all duration-300
          `}>
            {unlocked ? getAchievementIcon() : <Lock className="w-full h-full" />}
          </div>

          {/* Shine Effect */}
          {unlocked && isHovered && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30"
              initial={{ x: '-100%' }}
              animate={{ x: '100%' }}
              transition={{ duration: 0.6 }}
            />
          )}

          {/* Rarity Glow */}
          {unlocked && rarity === 'legendary' && (
            <motion.div
              className="absolute inset-0 rounded-full bg-gradient-to-r from-yellow-400/20 via-orange-500/20 to-red-500/20"
              animate={{ 
                scale: [1, 1.1, 1],
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{ 
                duration: 2, 
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          )}
        </div>

        {/* Progress Ring (for incomplete achievements) */}
        {!unlocked && maxProgress && progress > 0 && (
          <svg className="absolute inset-0 w-full h-full -rotate-90">
            <circle
              cx="50%"
              cy="50%"
              r="45%"
              fill="none"
              stroke="rgba(255,255,255,0.1)"
              strokeWidth="3"
            />
            <motion.circle
              cx="50%"
              cy="50%"
              r="45%"
              fill="none"
              stroke="url(#progressGradient)"
              strokeWidth="3"
              strokeLinecap="round"
              strokeDasharray={`${2 * Math.PI * 45} ${2 * Math.PI * 45}`}
              initial={{ strokeDashoffset: 2 * Math.PI * 45 }}
              animate={{ 
                strokeDashoffset: 2 * Math.PI * 45 * (1 - progressPercentage / 100)
              }}
              transition={{ duration: 1, delay: 0.5 }}
            />
            <defs>
              <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#3B82F6" />
                <stop offset="100%" stopColor="#8B5CF6" />
              </linearGradient>
            </defs>
          </svg>
        )}

        {/* Rarity Stars */}
        {unlocked && (
          <div className="absolute -top-1 -right-1 flex">
            {Array.from({ length: rarity === 'legendary' ? 3 : rarity === 'epic' ? 2 : rarity === 'rare' ? 1 : 0 }).map((_, i) => (
              <motion.div
                key={i}
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ 
                  delay: 0.5 + i * 0.1,
                  type: "spring",
                  stiffness: 300
                }}
              >
                <Star className="w-3 h-3 text-yellow-400 fill-current" />
              </motion.div>
            ))}
          </div>
        )}

        {/* New Badge Indicator */}
        {unlocked && unlockedAt && Date.now() - unlockedAt.getTime() < 24 * 60 * 60 * 1000 && (
          <motion.div
            className="absolute -top-2 -left-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 1, type: "spring" }}
          >
            NEW!
          </motion.div>
        )}
      </motion.div>

      {/* Tooltip */}
      <AnimatePresence>
        {showTooltip && showDetails && (
          <motion.div
            className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50"
            initial={{ opacity: 0, y: 10, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            <div className="bg-black/90 backdrop-blur-sm border border-gray-700 rounded-lg p-4 max-w-xs">
              <div className="flex items-center gap-2 mb-2">
                <div className={`w-6 h-6 rounded-full bg-gradient-to-br ${colors.bg} flex items-center justify-center`}>
                  <div className="w-4 h-4 text-white">
                    {getAchievementIcon()}
                  </div>
                </div>
                <h3 className="font-bold text-white">{name}</h3>
                <span className={`text-xs px-2 py-1 rounded-full bg-gradient-to-r ${colors.bg} text-white capitalize`}>
                  {rarity}
                </span>
              </div>
              
              <p className="text-gray-300 text-sm mb-3">{description}</p>
              
              {!unlocked && maxProgress && (
                <div className="mb-3">
                  <div className="flex justify-between text-xs text-gray-400 mb-1">
                    <span>Progress</span>
                    <span>{progress}/{maxProgress}</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progressPercentage}%` }}
                    />
                  </div>
                </div>
              )}
              
              {reward && unlocked && (
                <div className="text-xs text-green-400 flex items-center gap-1">
                  <Award className="w-3 h-3" />
                  <span>Reward: {reward}</span>
                </div>
              )}
              
              {unlocked && unlockedAt && (
                <div className="text-xs text-gray-500 mt-2">
                  Unlocked: {unlockedAt.toLocaleDateString()}
                </div>
              )}
            </div>
            
            {/* Tooltip Arrow */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2">
              <div className="border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-black/90" />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Unlock Animation */}
      <AnimatePresence>
        {unlocked && animated && (
          <motion.div
            className="absolute inset-0 pointer-events-none"
            initial={{ scale: 1 }}
            animate={{ scale: [1, 1.5, 1] }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            {/* Particle Effects */}
            {Array.from({ length: 8 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-yellow-400 rounded-full"
                style={{
                  left: '50%',
                  top: '50%',
                }}
                initial={{ 
                  scale: 0,
                  x: 0,
                  y: 0
                }}
                animate={{ 
                  scale: [0, 1, 0],
                  x: Math.cos(i * Math.PI / 4) * 40,
                  y: Math.sin(i * Math.PI / 4) * 40
                }}
                transition={{ 
                  duration: 1,
                  delay: 1 + i * 0.1
                }}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedAchievementBadge;
