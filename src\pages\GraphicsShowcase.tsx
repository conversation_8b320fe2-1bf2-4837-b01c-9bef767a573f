import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { ShowroomBackground } from '@/components/graphics/EnhancedBackgrounds';
import EnhancedCarCard from '@/components/graphics/EnhancedCarCard';
import EnhancedEventCard from '@/components/graphics/EnhancedEventCard';
import EnhancedAchievementBadge from '@/components/graphics/EnhancedAchievementBadge';
import Enhanced3DCarViewer from '@/components/graphics/Enhanced3DCarViewer';
import ImageGallery, { ImageGrid } from '@/components/graphics/ImageGallery';
import { PerformanceMonitor } from '@/components/graphics/GraphicsOptimizer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { carImages, eventImages } from '@/assets/images/cars';
import { <PERSON>, Palette, Zap, Star, Settings } from 'lucide-react';

const GraphicsShowcase = () => {
  const [selectedGallery, setSelectedGallery] = useState<number | null>(null);
  const [isRevving, setIsRevving] = useState(false);

  // Sample data for showcase
  const sampleCar = {
    id: 'rx3-showcase',
    name: 'Mazda RX-3 Savanna',
    model: 'RX-3',
    year: '1973',
    image: carImages.rx3.main,
    stats: {
      power: 95,
      handling: 88,
      acceleration: 92,
      braking: 85,
      topSpeed: 180,
      weight: 1100
    },
    rarity: 'legendary' as const,
    price: 45000,
    owned: true,
    foundersEdition: true
  };

  const sampleEvent = {
    id: 'burnout-showcase',
    title: 'Australian Burnout Championship',
    description: 'The ultimate test of tire-smoking skills in the heart of Australia',
    type: 'burnout' as const,
    date: '2024-02-15',
    time: '7:00 PM',
    location: 'Sydney Motorsport Park',
    maxParticipants: 50,
    currentParticipants: 42,
    entryFee: 25,
    prizes: {
      first: '$5,000 + Trophy',
      second: '$2,500 + Medal',
      third: '$1,000 + Certificate'
    },
    requirements: {
      minLevel: 5,
      foundersOnly: false
    },
    status: 'registration' as const,
    registered: false,
    featured: true
  };

  const sampleAchievements = [
    {
      id: 'welcome',
      name: 'Welcome to the Family!',
      description: 'Register your account and join the community',
      category: 'special' as const,
      rarity: 'common' as const,
      unlocked: true,
      unlockedAt: new Date('2024-01-01')
    },
    {
      id: 'first_burnout',
      name: "Smoke 'Em If You Got 'Em",
      description: 'Complete your first burnout challenge',
      category: 'driving' as const,
      rarity: 'rare' as const,
      unlocked: true,
      unlockedAt: new Date('2024-01-15')
    },
    {
      id: 'legend',
      name: 'Aussie Legend',
      description: 'Reach legendary status in the community',
      category: 'special' as const,
      rarity: 'legendary' as const,
      unlocked: false,
      progress: 75,
      maxProgress: 100
    }
  ];

  const galleryImages = [
    {
      id: '1',
      src: carImages.rx3.main,
      alt: 'Mazda RX-3 Savanna',
      title: 'Mazda RX-3 Savanna',
      description: 'The iconic rotary-powered legend',
      type: 'car' as const,
      category: 'rx3'
    },
    {
      id: '2',
      src: carImages.commodore.main,
      alt: 'Holden Commodore VL',
      title: 'Holden Commodore VL Turbo',
      description: 'Australian muscle at its finest',
      type: 'car' as const,
      category: 'commodore'
    },
    {
      id: '3',
      src: eventImages.burnout.championship,
      alt: 'Burnout Championship',
      title: 'Burnout Championship',
      description: 'Where rubber meets the road',
      type: 'event' as const,
      category: 'burnout'
    },
    {
      id: '4',
      src: eventImages.drift.series,
      alt: 'Drift Series',
      title: 'Professional Drift Series',
      description: 'Sideways action at its best',
      type: 'event' as const,
      category: 'drift'
    }
  ];

  return (
    <ShowroomBackground>
      <Navbar />
      
      <main className="container mx-auto px-4 py-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-5xl font-bold text-white mb-4">
            Graphics <span className="text-racing-yellow">Showcase</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Experience the enhanced visual system of Aussie Burnout Legends with 
            high-quality images, smooth animations, and professional graphics.
          </p>
        </motion.div>

        <Tabs defaultValue="components" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="components" className="flex items-center gap-2">
              <Palette className="w-4 h-4" />
              Components
            </TabsTrigger>
            <TabsTrigger value="3d" className="flex items-center gap-2">
              <Zap className="w-4 h-4" />
              3D Viewer
            </TabsTrigger>
            <TabsTrigger value="gallery" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Gallery
            </TabsTrigger>
            <TabsTrigger value="performance" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Performance
            </TabsTrigger>
          </TabsList>

          <TabsContent value="components" className="space-y-8">
            {/* Enhanced Car Card Showcase */}
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <CardTitle className="text-racing-yellow">Enhanced Car Cards</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <EnhancedCarCard {...sampleCar} />
                  <EnhancedCarCard 
                    {...sampleCar} 
                    id="commodore-showcase"
                    name="Holden Commodore VL"
                    image={carImages.commodore.main}
                    rarity="rare"
                    owned={false}
                    foundersEdition={false}
                  />
                  <EnhancedCarCard 
                    {...sampleCar} 
                    id="falcon-showcase"
                    name="Ford Falcon XB GT"
                    image={carImages.falcon.main}
                    rarity="epic"
                    locked={true}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Event Card Showcase */}
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <CardTitle className="text-racing-yellow">Enhanced Event Cards</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <EnhancedEventCard {...sampleEvent} />
                  <EnhancedEventCard 
                    {...sampleEvent}
                    id="drift-showcase"
                    title="Drift Masters Series"
                    type="drift"
                    featured={false}
                    registered={true}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Achievement Badges Showcase */}
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <CardTitle className="text-racing-yellow">Enhanced Achievement Badges</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap justify-center gap-8">
                  {sampleAchievements.map((achievement) => (
                    <EnhancedAchievementBadge
                      key={achievement.id}
                      {...achievement}
                      size="xl"
                      showDetails={true}
                      animated={true}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="3d" className="space-y-8">
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <CardTitle className="text-racing-yellow flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  Enhanced 3D Car Viewer
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <Enhanced3DCarViewer
                    isRevving={isRevving}
                    onRevEngine={() => {
                      setIsRevving(true);
                      setTimeout(() => setIsRevving(false), 3000);
                    }}
                    customization={{
                      bodyColor: '#FF0000',
                      hasRacingStripes: true,
                      hasAustralianFlag: true,
                      wheelSize: 18,
                      paintFinish: 'metallic',
                      underglow: true,
                      spoiler: true
                    }}
                    autoRotate={true}
                    showControls={true}
                    quality="ultra"
                    environment="showroom"
                  />
                  
                  <div className="text-center">
                    <p className="text-gray-300 mb-4">
                      Interactive 3D model with real-time customization, multiple camera angles, 
                      and environment options. Try the controls above!
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="gallery" className="space-y-8">
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <CardTitle className="text-racing-yellow">Image Gallery System</CardTitle>
              </CardHeader>
              <CardContent>
                <ImageGrid
                  images={galleryImages}
                  columns={2}
                  onImageClick={(index) => setSelectedGallery(index)}
                />
                
                <div className="mt-6 text-center">
                  <p className="text-gray-300">
                    Click any image to open the full gallery experience with zoom, 
                    navigation, and download options.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-8">
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <CardTitle className="text-racing-yellow">Performance & Optimization</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-white font-semibold">Features Implemented:</h3>
                    <ul className="text-gray-300 space-y-2">
                      <li>✅ Automatic image optimization</li>
                      <li>✅ Lazy loading with intersection observer</li>
                      <li>✅ WebP format conversion</li>
                      <li>✅ Responsive image sizing</li>
                      <li>✅ Performance-based quality adjustment</li>
                      <li>✅ GPU-accelerated animations</li>
                      <li>✅ Memory usage monitoring</li>
                      <li>✅ Reduced motion support</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-white font-semibold">Image Sources:</h3>
                    <ul className="text-gray-300 space-y-2">
                      <li>🏎️ High-quality car photography</li>
                      <li>🏁 Professional event imagery</li>
                      <li>🎨 Enhanced SVG placeholders</li>
                      <li>🌅 Dynamic background systems</li>
                      <li>⚡ Real-time optimization</li>
                      <li>📱 Mobile-optimized delivery</li>
                      <li>🔄 Automatic fallback system</li>
                      <li>💾 Intelligent caching</li>
                    </ul>
                  </div>
                </div>
                
                <div className="bg-gray-800/50 rounded-lg p-4">
                  <h4 className="text-racing-yellow font-semibold mb-2">Live Performance Monitor:</h4>
                  <p className="text-gray-300 text-sm">
                    The performance monitor in the bottom-left corner shows real-time FPS, 
                    performance level, and memory usage to ensure optimal experience.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      <Footer />
      
      {/* Image Gallery Modal */}
      {selectedGallery !== null && (
        <ImageGallery
          images={galleryImages}
          isOpen={selectedGallery !== null}
          onClose={() => setSelectedGallery(null)}
          initialIndex={selectedGallery}
        />
      )}
      
      {/* Performance Monitor */}
      <PerformanceMonitor />
    </ShowroomBackground>
  );
};

export default GraphicsShowcase;
