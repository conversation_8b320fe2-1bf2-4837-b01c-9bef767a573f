import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { BookOpen, Video, Wrench, Zap, Car, Trophy, Clock, Star } from 'lucide-react';
import AustralianEmblem from '@/components/AustralianEmblem';

interface EducationalArticle {
  id: string;
  title: string;
  description: string;
  category: 'basics' | 'tuning' | 'history' | 'techniques';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  readTime: number;
  rating: number;
  content: React.ReactNode;
  tags: string[];
}

const EDUCATIONAL_CONTENT: EducationalArticle[] = [
  {
    id: 'burnout-basics',
    title: 'Burnout Basics: Getting Started',
    description: 'Learn the fundamentals of performing safe and effective burnouts',
    category: 'basics',
    difficulty: 'beginner',
    readTime: 5,
    rating: 4.8,
    tags: ['burnout', 'safety', 'basics'],
    content: (
      <div className="space-y-6">
        <div>
          <h3 className="text-xl font-semibold text-white mb-3">What is a Burnout?</h3>
          <p className="text-gray-300 mb-4">
            A burnout is when you spin the rear wheels of your car while keeping the front wheels 
            stationary, creating smoke from the heated tires. It's a staple of Australian car culture 
            and a crowd favorite at car shows.
          </p>
        </div>
        
        <div className="bg-racing-yellow/10 border border-racing-yellow/20 rounded-lg p-4">
          <h4 className="text-racing-yellow font-semibold mb-2">⚠️ Safety First</h4>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• Always perform burnouts in a safe, controlled environment</li>
            <li>• Ensure adequate ventilation to avoid smoke inhalation</li>
            <li>• Check local laws and regulations</li>
            <li>• Never attempt on public roads</li>
          </ul>
        </div>
        
        <div>
          <h4 className="text-white font-semibold mb-3">Basic Technique:</h4>
          <ol className="text-gray-300 space-y-2">
            <li><strong>1. Warm up your car:</strong> Let the engine reach operating temperature</li>
            <li><strong>2. Position correctly:</strong> Ensure you have enough space and proper surface</li>
            <li><strong>3. Apply brakes:</strong> Hold the brake pedal firmly with your left foot</li>
            <li><strong>4. Rev and release:</strong> Give it throttle while controlling with the brake</li>
            <li><strong>5. Control the spin:</strong> Modulate throttle to maintain the burnout</li>
          </ol>
        </div>
        
        <div className="bg-gray-800 p-4 rounded-lg">
          <h4 className="text-white font-semibold mb-2">Pro Tips:</h4>
          <ul className="text-gray-300 text-sm space-y-1">
            <li>• Start with shorter burnouts and build up</li>
            <li>• Use older tires for practice</li>
            <li>• Keep RPMs reasonable to avoid engine damage</li>
            <li>• Practice throttle control for better smoke production</li>
          </ul>
        </div>
      </div>
    )
  },
  {
    id: 'rx3-history',
    title: 'The Legendary Mazda RX-3',
    description: 'Discover the history and significance of the iconic Mazda RX-3 in Australian motorsport',
    category: 'history',
    difficulty: 'beginner',
    readTime: 8,
    rating: 4.9,
    tags: ['mazda', 'rx-3', 'history', 'rotary'],
    content: (
      <div className="space-y-6">
        <div>
          <h3 className="text-xl font-semibold text-white mb-3">The Birth of a Legend</h3>
          <p className="text-gray-300 mb-4">
            The Mazda RX-3, known as the Savanna in some markets, was produced from 1971 to 1978. 
            It became an icon of Australian motorsport and street culture, particularly known for 
            its distinctive rotary engine sound and impressive performance.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-800 p-4 rounded-lg">
            <h4 className="text-racing-yellow font-semibold mb-2">Key Specifications</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Engine: 12A Rotary (1.1L equivalent)</li>
              <li>• Power: 105-130 HP (depending on variant)</li>
              <li>• Weight: ~1,000 kg</li>
              <li>• Top Speed: ~180 km/h</li>
            </ul>
          </div>
          
          <div className="bg-gray-800 p-4 rounded-lg">
            <h4 className="text-racing-yellow font-semibold mb-2">Racing Heritage</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Multiple Bathurst victories</li>
              <li>• ATCC championship wins</li>
              <li>• Dominant in Group C racing</li>
              <li>• Street machine culture icon</li>
            </ul>
          </div>
        </div>
        
        <div>
          <h4 className="text-white font-semibold mb-3">The Rotary Advantage</h4>
          <p className="text-gray-300 mb-4">
            The RX-3's rotary engine provided several advantages over conventional piston engines:
          </p>
          <ul className="text-gray-300 space-y-2">
            <li><strong>Smooth Power Delivery:</strong> No reciprocating parts meant vibration-free operation</li>
            <li><strong>High RPM Capability:</strong> Could rev to 7,000+ RPM reliably</li>
            <li><strong>Compact Size:</strong> Smaller and lighter than equivalent piston engines</li>
            <li><strong>Distinctive Sound:</strong> The unique "brap-brap" exhaust note</li>
          </ul>
        </div>
        
        <div className="bg-racing-red/10 border border-racing-red/20 rounded-lg p-4">
          <h4 className="text-racing-red font-semibold mb-2">Australian Connection</h4>
          <p className="text-gray-300 text-sm">
            The RX-3 holds a special place in Australian automotive culture. It was affordable, 
            reliable, and had the performance to compete with much more expensive cars. This made 
            it a favorite among young enthusiasts and helped establish the foundation of Australia's 
            street machine culture.
          </p>
        </div>
      </div>
    )
  },
  {
    id: 'drift-techniques',
    title: 'Advanced Drift Techniques',
    description: 'Master the art of controlled sliding with these advanced drifting techniques',
    category: 'techniques',
    difficulty: 'advanced',
    readTime: 12,
    rating: 4.7,
    tags: ['drift', 'techniques', 'advanced', 'control'],
    content: (
      <div className="space-y-6">
        <div>
          <h3 className="text-xl font-semibold text-white mb-3">Understanding Drift Physics</h3>
          <p className="text-gray-300 mb-4">
            Drifting is about maintaining controlled oversteer through a corner. It requires 
            precise throttle control, steering input, and understanding of weight transfer.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-900/20 border border-blue-500/20 p-4 rounded-lg">
            <h4 className="text-blue-400 font-semibold mb-2">Initiation</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Clutch kick</li>
              <li>• Handbrake pull</li>
              <li>• Power over</li>
              <li>• Feint motion</li>
            </ul>
          </div>
          
          <div className="bg-purple-900/20 border border-purple-500/20 p-4 rounded-lg">
            <h4 className="text-purple-400 font-semibold mb-2">Maintenance</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Throttle control</li>
              <li>• Counter-steering</li>
              <li>• Weight transfer</li>
              <li>• Line selection</li>
            </ul>
          </div>
          
          <div className="bg-green-900/20 border border-green-500/20 p-4 rounded-lg">
            <h4 className="text-green-400 font-semibold mb-2">Transition</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Smooth inputs</li>
              <li>• Momentum control</li>
              <li>• Exit strategy</li>
              <li>• Speed management</li>
            </ul>
          </div>
        </div>
        
        <div>
          <h4 className="text-white font-semibold mb-3">Advanced Techniques:</h4>
          
          <div className="space-y-4">
            <div className="bg-gray-800 p-4 rounded-lg">
              <h5 className="text-racing-yellow font-semibold mb-2">Clutch Kick Drift</h5>
              <p className="text-gray-300 text-sm mb-2">
                Quickly depress and release the clutch while on throttle to break rear traction.
              </p>
              <div className="text-xs text-gray-400">
                <strong>When to use:</strong> Mid-corner corrections, tight corners
              </div>
            </div>
            
            <div className="bg-gray-800 p-4 rounded-lg">
              <h5 className="text-racing-yellow font-semibold mb-2">Feint Motion</h5>
              <p className="text-gray-300 text-sm mb-2">
                Quickly steer away from the corner before initiating to load the outside tires.
              </p>
              <div className="text-xs text-gray-400">
                <strong>When to use:</strong> High-speed entries, weight transfer initiation
              </div>
            </div>
            
            <div className="bg-gray-800 p-4 rounded-lg">
              <h5 className="text-racing-yellow font-semibold mb-2">Manji Drifting</h5>
              <p className="text-gray-300 text-sm mb-2">
                Continuous left-right-left transitions in a snake-like pattern.
              </p>
              <div className="text-xs text-gray-400">
                <strong>When to use:</strong> Straight sections, show drifting
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-racing-yellow/10 border border-racing-yellow/20 rounded-lg p-4">
          <h4 className="text-racing-yellow font-semibold mb-2">⚠️ Safety Reminder</h4>
          <p className="text-gray-300 text-sm">
            Advanced drifting techniques should only be practiced in controlled environments 
            with proper safety equipment. Always prioritize safety over style.
          </p>
        </div>
      </div>
    )
  },
  {
    id: 'engine-tuning',
    title: 'Engine Tuning Fundamentals',
    description: 'Learn the basics of engine tuning to maximize performance',
    category: 'tuning',
    difficulty: 'intermediate',
    readTime: 15,
    rating: 4.6,
    tags: ['tuning', 'engine', 'performance', 'modification'],
    content: (
      <div className="space-y-6">
        <div>
          <h3 className="text-xl font-semibold text-white mb-3">Understanding Engine Tuning</h3>
          <p className="text-gray-300 mb-4">
            Engine tuning is the process of modifying and adjusting engine parameters to 
            optimize performance, efficiency, or reliability. It's both an art and a science.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-white font-semibold mb-3">Basic Modifications</h4>
            <div className="space-y-3">
              <div className="bg-gray-800 p-3 rounded">
                <h5 className="text-racing-yellow font-medium">Cold Air Intake</h5>
                <p className="text-xs text-gray-400">Increases airflow to the engine</p>
              </div>
              <div className="bg-gray-800 p-3 rounded">
                <h5 className="text-racing-yellow font-medium">Exhaust System</h5>
                <p className="text-xs text-gray-400">Improves exhaust flow and sound</p>
              </div>
              <div className="bg-gray-800 p-3 rounded">
                <h5 className="text-racing-yellow font-medium">ECU Tune</h5>
                <p className="text-xs text-gray-400">Optimizes fuel and ignition maps</p>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="text-white font-semibold mb-3">Advanced Modifications</h4>
            <div className="space-y-3">
              <div className="bg-gray-800 p-3 rounded">
                <h5 className="text-racing-yellow font-medium">Turbocharger</h5>
                <p className="text-xs text-gray-400">Forced induction for more power</p>
              </div>
              <div className="bg-gray-800 p-3 rounded">
                <h5 className="text-racing-yellow font-medium">Internal Engine Work</h5>
                <p className="text-xs text-gray-400">Pistons, rods, head work</p>
              </div>
              <div className="bg-gray-800 p-3 rounded">
                <h5 className="text-racing-yellow font-medium">Fuel System</h5>
                <p className="text-xs text-gray-400">Injectors, pump, rail upgrades</p>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <h4 className="text-white font-semibold mb-3">Tuning Process</h4>
          <ol className="text-gray-300 space-y-2">
            <li><strong>1. Baseline:</strong> Establish current performance metrics</li>
            <li><strong>2. Modifications:</strong> Install hardware modifications</li>
            <li><strong>3. Mapping:</strong> Adjust ECU parameters</li>
            <li><strong>4. Testing:</strong> Dyno testing and road testing</li>
            <li><strong>5. Refinement:</strong> Fine-tune for optimal performance</li>
          </ol>
        </div>
        
        <div className="bg-racing-red/10 border border-racing-red/20 rounded-lg p-4">
          <h4 className="text-racing-red font-semibold mb-2">⚠️ Important Considerations</h4>
          <ul className="text-gray-300 text-sm space-y-1">
            <li>• Always tune with a qualified professional</li>
            <li>• Consider reliability vs. performance trade-offs</li>
            <li>• Ensure supporting modifications are adequate</li>
            <li>• Regular maintenance becomes more critical</li>
          </ul>
        </div>
      </div>
    )
  }
];

interface EducationalContentProps {
  className?: string;
}

const EducationalContent: React.FC<EducationalContentProps> = ({ className }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedArticle, setSelectedArticle] = useState<EducationalArticle | null>(null);

  const filteredContent = selectedCategory === 'all' 
    ? EDUCATIONAL_CONTENT 
    : EDUCATIONAL_CONTENT.filter(article => article.category === selectedCategory);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-500';
      case 'intermediate': return 'bg-yellow-500';
      case 'advanced': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'basics': return <BookOpen className="w-4 h-4" />;
      case 'tuning': return <Wrench className="w-4 h-4" />;
      case 'history': return <Trophy className="w-4 h-4" />;
      case 'techniques': return <Zap className="w-4 h-4" />;
      default: return <Car className="w-4 h-4" />;
    }
  };

  if (selectedArticle) {
    return (
      <div className={`${className}`}>
        <Card className="bg-black/40 border-racing-yellow/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={() => setSelectedArticle(null)}
                className="mb-4"
              >
                ← Back to Articles
              </Button>
              <AustralianEmblem size="sm" className="opacity-70" />
            </div>
            
            <div className="flex items-start gap-4">
              <div className="p-3 bg-racing-yellow/20 rounded-lg">
                {getCategoryIcon(selectedArticle.category)}
              </div>
              <div className="flex-1">
                <CardTitle className="text-2xl text-racing-yellow mb-2">
                  {selectedArticle.title}
                </CardTitle>
                <CardDescription className="text-gray-400 mb-4">
                  {selectedArticle.description}
                </CardDescription>
                
                <div className="flex items-center gap-4 text-sm">
                  <Badge className={`${getDifficultyColor(selectedArticle.difficulty)} text-white`}>
                    {selectedArticle.difficulty}
                  </Badge>
                  <div className="flex items-center gap-1 text-gray-400">
                    <Clock className="w-4 h-4" />
                    <span>{selectedArticle.readTime} min read</span>
                  </div>
                  <div className="flex items-center gap-1 text-gray-400">
                    <Star className="w-4 h-4 text-yellow-500" />
                    <span>{selectedArticle.rating}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            {selectedArticle.content}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <Card className="bg-black/40 border-racing-yellow/20">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl text-racing-yellow">Educational Content</CardTitle>
              <CardDescription className="text-gray-400">
                Learn about car culture, techniques, and history
              </CardDescription>
            </div>
            <AustralianEmblem size="sm" className="opacity-70" />
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid grid-cols-5 mb-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="basics">Basics</TabsTrigger>
              <TabsTrigger value="techniques">Techniques</TabsTrigger>
              <TabsTrigger value="tuning">Tuning</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredContent.map((article) => (
                <Card 
                  key={article.id}
                  className="bg-gray-900/40 border-gray-700 hover:border-racing-yellow/40 transition-colors cursor-pointer"
                  onClick={() => setSelectedArticle(article)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start gap-3 mb-4">
                      <div className="p-2 bg-racing-yellow/20 rounded">
                        {getCategoryIcon(article.category)}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-white mb-1">{article.title}</h3>
                        <p className="text-sm text-gray-400 line-clamp-2">{article.description}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <Badge className={`${getDifficultyColor(article.difficulty)} text-white text-xs`}>
                          {article.difficulty}
                        </Badge>
                        <div className="flex items-center gap-1 text-gray-400">
                          <Clock className="w-3 h-3" />
                          <span>{article.readTime}m</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1 text-gray-400">
                        <Star className="w-3 h-3 text-yellow-500" />
                        <span>{article.rating}</span>
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mt-3">
                      {article.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default EducationalContent;
