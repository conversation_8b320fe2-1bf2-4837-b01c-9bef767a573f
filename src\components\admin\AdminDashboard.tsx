import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  Users, 
  Car, 
  Trophy, 
  Calendar, 
  BarChart3, 
  AlertTriangle, 
  Settings,
  Download,
  RefreshCw,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';
import { analytics } from '@/utils/analytics';
import AustralianEmblem from '@/components/AustralianEmblem';

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalCars: number;
  totalEvents: number;
  totalAchievements: number;
  errorCount: number;
  performanceScore: number;
}

interface UserData {
  id: string;
  username: string;
  email: string;
  joinDate: string;
  lastActive: string;
  totalScore: number;
  achievements: number;
  carsOwned: number;
  foundersEdition: boolean;
  status: 'active' | 'inactive' | 'banned';
}

interface EventData {
  id: string;
  title: string;
  type: string;
  date: string;
  participants: number;
  maxParticipants: number;
  status: 'upcoming' | 'ongoing' | 'completed';
}

interface ErrorData {
  id: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
  userId?: string;
  url: string;
  count: number;
}

const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalCars: 4,
    totalEvents: 0,
    totalAchievements: 7,
    errorCount: 0,
    performanceScore: 95
  });

  const [users, setUsers] = useState<UserData[]>([]);
  const [events, setEvents] = useState<EventData[]>([]);
  const [errors, setErrors] = useState<ErrorData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setIsLoading(true);
    
    // Simulate loading data from various sources
    try {
      // Load analytics data
      const analyticsData = analytics.getAnalyticsData();
      
      // Process user data from localStorage
      const userData = JSON.parse(localStorage.getItem('users') || '[]');
      const processedUsers = userData.map((user: any, index: number) => ({
        id: user.id || `user_${index}`,
        username: user.username || `User ${index + 1}`,
        email: user.email || `user${index + 1}@example.com`,
        joinDate: user.joinDate || new Date().toISOString(),
        lastActive: user.lastActive || new Date().toISOString(),
        totalScore: user.totalScore || Math.floor(Math.random() * 10000),
        achievements: user.achievements?.length || Math.floor(Math.random() * 7),
        carsOwned: user.carsOwned?.length || Math.floor(Math.random() * 4) + 1,
        foundersEdition: user.foundersEdition || false,
        status: user.status || 'active'
      }));

      // Generate sample events
      const sampleEvents: EventData[] = [
        {
          id: 'event_1',
          title: 'Aussie Burnout Championship',
          type: 'burnout',
          date: '2024-02-15',
          participants: 32,
          maxParticipants: 50,
          status: 'upcoming'
        },
        {
          id: 'event_2',
          title: 'Founders Drift Series',
          type: 'drift',
          date: '2024-02-20',
          participants: 18,
          maxParticipants: 25,
          status: 'upcoming'
        }
      ];

      // Process error data from analytics
      const errorData = analyticsData
        .flatMap(session => session.errors || [])
        .reduce((acc: any[], error: any) => {
          const existing = acc.find(e => e.message === error.message);
          if (existing) {
            existing.count++;
          } else {
            acc.push({
              id: error.id,
              message: error.message,
              severity: error.severity,
              timestamp: error.timestamp,
              userId: error.userId,
              url: error.url,
              count: 1
            });
          }
          return acc;
        }, [])
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      setUsers(processedUsers);
      setEvents(sampleEvents);
      setErrors(errorData);
      
      setStats({
        totalUsers: processedUsers.length,
        activeUsers: processedUsers.filter(u => u.status === 'active').length,
        totalCars: 4,
        totalEvents: sampleEvents.length,
        totalAchievements: 7,
        errorCount: errorData.length,
        performanceScore: 95 - Math.min(errorData.length * 2, 30)
      });
      
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const exportData = (type: 'users' | 'events' | 'analytics' | 'errors') => {
    let data: any;
    let filename: string;
    
    switch (type) {
      case 'users':
        data = users;
        filename = 'users_export.json';
        break;
      case 'events':
        data = events;
        filename = 'events_export.json';
        break;
      case 'analytics':
        data = analytics.getAnalyticsData();
        filename = 'analytics_export.json';
        break;
      case 'errors':
        data = errors;
        filename = 'errors_export.json';
        break;
      default:
        return;
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'high': return 'bg-orange-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'inactive': return 'bg-yellow-500';
      case 'banned': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-racing-yellow mx-auto mb-4" />
          <p className="text-white">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#000424] to-[#001440] p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
            <p className="text-gray-400">Manage Aussie Burnout Legends platform</p>
          </div>
          <div className="flex items-center gap-4">
            <Button
              onClick={loadDashboardData}
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </Button>
            <AustralianEmblem size="sm" />
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-black/40 border-racing-yellow/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Total Users</p>
                  <p className="text-2xl font-bold text-white">{stats.totalUsers}</p>
                  <p className="text-green-400 text-xs">{stats.activeUsers} active</p>
                </div>
                <Users className="w-8 h-8 text-racing-yellow" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/40 border-racing-yellow/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Cars Available</p>
                  <p className="text-2xl font-bold text-white">{stats.totalCars}</p>
                  <p className="text-blue-400 text-xs">4 models</p>
                </div>
                <Car className="w-8 h-8 text-racing-yellow" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/40 border-racing-yellow/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Active Events</p>
                  <p className="text-2xl font-bold text-white">{stats.totalEvents}</p>
                  <p className="text-purple-400 text-xs">Upcoming</p>
                </div>
                <Calendar className="w-8 h-8 text-racing-yellow" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/40 border-racing-yellow/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">System Health</p>
                  <p className="text-2xl font-bold text-white">{stats.performanceScore}%</p>
                  <p className="text-orange-400 text-xs">{stats.errorCount} errors</p>
                </div>
                <BarChart3 className="w-8 h-8 text-racing-yellow" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="users" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full max-w-md">
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="errors">Errors</TabsTrigger>
          </TabsList>

          <TabsContent value="users">
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-racing-yellow">User Management</CardTitle>
                    <CardDescription>Manage registered users and their data</CardDescription>
                  </div>
                  <Button
                    onClick={() => exportData('users')}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Export
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {users.slice(0, 10).map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-4 bg-gray-900/40 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold text-white">{user.username}</h3>
                            {user.foundersEdition && (
                              <Badge className="bg-purple-500 text-white text-xs">Founders</Badge>
                            )}
                            <Badge className={`${getStatusColor(user.status)} text-white text-xs`}>
                              {user.status}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-400">{user.email}</p>
                          <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                            <span>Score: {user.totalScore}</span>
                            <span>Achievements: {user.achievements}</span>
                            <span>Cars: {user.carsOwned}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="text-red-400">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="events">
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-racing-yellow">Event Management</CardTitle>
                    <CardDescription>Manage events and competitions</CardDescription>
                  </div>
                  <Button
                    onClick={() => exportData('events')}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Export
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {events.map((event) => (
                    <div key={event.id} className="p-4 bg-gray-900/40 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-white">{event.title}</h3>
                        <Badge className="bg-blue-500 text-white text-xs">{event.type}</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-400">
                          <p>Date: {event.date}</p>
                          <p>Participants: {event.participants}/{event.maxParticipants}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Progress 
                            value={(event.participants / event.maxParticipants) * 100} 
                            className="w-24 h-2"
                          />
                          <span className="text-xs text-gray-400">
                            {Math.round((event.participants / event.maxParticipants) * 100)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-racing-yellow">Analytics Overview</CardTitle>
                    <CardDescription>Platform usage and performance metrics</CardDescription>
                  </div>
                  <Button
                    onClick={() => exportData('analytics')}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Export
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-gray-900/40 p-4 rounded-lg">
                    <h4 className="text-white font-semibold mb-2">Performance Score</h4>
                    <div className="flex items-center gap-2">
                      <Progress value={stats.performanceScore} className="flex-1" />
                      <span className="text-white">{stats.performanceScore}%</span>
                    </div>
                  </div>
                  
                  <div className="bg-gray-900/40 p-4 rounded-lg">
                    <h4 className="text-white font-semibold mb-2">User Engagement</h4>
                    <div className="flex items-center gap-2">
                      <Progress value={85} className="flex-1" />
                      <span className="text-white">85%</span>
                    </div>
                  </div>
                  
                  <div className="bg-gray-900/40 p-4 rounded-lg">
                    <h4 className="text-white font-semibold mb-2">System Uptime</h4>
                    <div className="flex items-center gap-2">
                      <Progress value={99.9} className="flex-1" />
                      <span className="text-white">99.9%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="errors">
            <Card className="bg-black/40 border-racing-yellow/20">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-racing-yellow">Error Monitoring</CardTitle>
                    <CardDescription>System errors and issues</CardDescription>
                  </div>
                  <Button
                    onClick={() => exportData('errors')}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Export
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {errors.length === 0 ? (
                    <div className="text-center py-8">
                      <AlertTriangle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-xl text-white mb-2">No Errors Found</h3>
                      <p className="text-gray-400">System is running smoothly!</p>
                    </div>
                  ) : (
                    errors.map((error) => (
                      <div key={error.id} className="p-4 bg-gray-900/40 rounded-lg">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge className={`${getSeverityColor(error.severity)} text-white text-xs`}>
                                {error.severity}
                              </Badge>
                              <span className="text-xs text-gray-400">
                                Count: {error.count}
                              </span>
                            </div>
                            <h3 className="font-semibold text-white mb-1">{error.message}</h3>
                            <p className="text-sm text-gray-400">{error.url}</p>
                            <p className="text-xs text-gray-500">
                              {new Date(error.timestamp).toLocaleString()}
                            </p>
                          </div>
                          <Button size="sm" variant="outline">
                            <Eye className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminDashboard;
