import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Car, Settings, Star, Trophy, Zap, Gauge, Wrench, Crown } from 'lucide-react';
import { useAuth, CarData } from '@/context/AuthContext';
import Car3DViewer from '@/components/3D/Car3DViewer';
import CarCustomizer from '@/components/CarCustomizer';
import AustralianEmblem from '@/components/AustralianEmblem';
import { useToast } from '@/components/ui/use-toast';

interface EnhancedGarageProps {
  className?: string;
}

const AVAILABLE_CARS: CarData[] = [
  {
    id: 'holden-commodore',
    name: 'Holden Commodore VL Turbo',
    model: 'Holden Commodore VL',
    customization: {
      bodyColor: '#FFFFFF',
      hasRacingStripes: true,
      hasAustralianFlag: true,
      wheelSize: 18,
      exhaustType: 'sport'
    },
    stats: {
      power: 95,
      handling: 85,
      acceleration: 90,
      braking: 80
    },
    unlocked: false
  },
  {
    id: 'ford-falcon',
    name: 'Ford Falcon XB GT',
    model: 'Ford Falcon XB',
    customization: {
      bodyColor: '#000000',
      hasRacingStripes: false,
      hasAustralianFlag: true,
      wheelSize: 17,
      exhaustType: 'racing'
    },
    stats: {
      power: 100,
      handling: 80,
      acceleration: 95,
      braking: 75
    },
    unlocked: false
  },
  {
    id: 'torana-xu1',
    name: 'Holden Torana XU-1',
    model: 'Holden Torana',
    customization: {
      bodyColor: '#FFD700',
      hasRacingStripes: true,
      hasAustralianFlag: true,
      wheelSize: 16,
      exhaustType: 'sport'
    },
    stats: {
      power: 80,
      handling: 90,
      acceleration: 85,
      braking: 85
    },
    unlocked: false
  }
];

const EnhancedGarage: React.FC<EnhancedGarageProps> = ({ className }) => {
  const { user, addCarToGarage, updateCarCustomization } = useAuth();
  const { toast } = useToast();
  const [selectedCar, setSelectedCar] = useState<CarData | null>(user?.garage[0] || null);
  const [activeTab, setActiveTab] = useState('collection');

  const handleCarSelect = (car: CarData) => {
    setSelectedCar(car);
  };

  const handleCustomizationChange = (customization: CarData['customization']) => {
    if (selectedCar && user) {
      updateCarCustomization(selectedCar.id, customization);
      setSelectedCar({ ...selectedCar, customization });
    }
  };

  const handlePurchaseCar = (car: CarData) => {
    if (user) {
      const newCar = {
        ...car,
        unlocked: true,
        purchaseDate: new Date().toISOString()
      };
      addCarToGarage(newCar);
    }
  };

  const getStatColor = (value: number) => {
    if (value >= 90) return 'bg-green-500';
    if (value >= 70) return 'bg-yellow-500';
    if (value >= 50) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getOverallRating = (stats: CarData['stats']) => {
    return Math.round((stats.power + stats.handling + stats.acceleration + stats.braking) / 4);
  };

  if (!user) {
    return (
      <div className={`${className} text-center py-12`}>
        <Car className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl text-white mb-2">Garage Access Required</h3>
        <p className="text-gray-400">Please log in to access your garage and car collection.</p>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <Card className="bg-black/40 border-racing-yellow/20">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl text-racing-yellow flex items-center gap-2">
                <Car className="w-6 h-6" />
                {user.foundersEdition && <Crown className="w-5 h-5" />}
                {user.username}'s Garage
              </CardTitle>
              <CardDescription className="text-gray-400">
                Manage your car collection and customizations
              </CardDescription>
            </div>
            <AustralianEmblem size="sm" className="opacity-70" />
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="collection">Collection</TabsTrigger>
              <TabsTrigger value="customize">Customize</TabsTrigger>
              <TabsTrigger value="shop">Car Shop</TabsTrigger>
            </TabsList>
            
            <TabsContent value="collection" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">Your Cars</h3>
                  {user.garage.map((car) => (
                    <Card 
                      key={car.id}
                      className={`cursor-pointer transition-all ${
                        selectedCar?.id === car.id 
                          ? 'bg-racing-yellow/20 border-racing-yellow' 
                          : 'bg-gray-900/40 border-gray-700 hover:border-gray-600'
                      }`}
                      onClick={() => handleCarSelect(car)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-white">{car.name}</h4>
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 text-yellow-500" />
                            <span className="text-sm text-yellow-500">{getOverallRating(car.stats)}</span>
                          </div>
                        </div>
                        <p className="text-sm text-gray-400 mb-3">{car.model}</p>
                        
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div className="flex items-center justify-between">
                            <span className="text-gray-400">Power</span>
                            <div className="flex items-center gap-1">
                              <div className={`w-2 h-2 rounded-full ${getStatColor(car.stats.power)}`}></div>
                              <span className="text-white">{car.stats.power}</span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-400">Handling</span>
                            <div className="flex items-center gap-1">
                              <div className={`w-2 h-2 rounded-full ${getStatColor(car.stats.handling)}`}></div>
                              <span className="text-white">{car.stats.handling}</span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-400">Acceleration</span>
                            <div className="flex items-center gap-1">
                              <div className={`w-2 h-2 rounded-full ${getStatColor(car.stats.acceleration)}`}></div>
                              <span className="text-white">{car.stats.acceleration}</span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-400">Braking</span>
                            <div className="flex items-center gap-1">
                              <div className={`w-2 h-2 rounded-full ${getStatColor(car.stats.braking)}`}></div>
                              <span className="text-white">{car.stats.braking}</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                
                <div className="space-y-4">
                  {selectedCar && (
                    <>
                      <h3 className="text-lg font-semibold text-white">Preview</h3>
                      <div className="rounded-lg overflow-hidden border-2 border-racing-yellow/20">
                        <Car3DViewer customization={selectedCar.customization} />
                      </div>
                      
                      <Card className="bg-gray-900/40 border-gray-700">
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg text-white">{selectedCar.name}</CardTitle>
                          <CardDescription>{selectedCar.model}</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-gray-400">Power</span>
                                <span className="text-sm text-white">{selectedCar.stats.power}/100</span>
                              </div>
                              <Progress value={selectedCar.stats.power} className="h-2" />
                            </div>
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-gray-400">Handling</span>
                                <span className="text-sm text-white">{selectedCar.stats.handling}/100</span>
                              </div>
                              <Progress value={selectedCar.stats.handling} className="h-2" />
                            </div>
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-gray-400">Acceleration</span>
                                <span className="text-sm text-white">{selectedCar.stats.acceleration}/100</span>
                              </div>
                              <Progress value={selectedCar.stats.acceleration} className="h-2" />
                            </div>
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-gray-400">Braking</span>
                                <span className="text-sm text-white">{selectedCar.stats.braking}/100</span>
                              </div>
                              <Progress value={selectedCar.stats.braking} className="h-2" />
                            </div>
                          </div>
                          
                          <div className="pt-2 border-t border-gray-700">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-400">Overall Rating</span>
                              <div className="flex items-center gap-1">
                                <Star className="w-4 h-4 text-yellow-500" />
                                <span className="text-lg font-bold text-yellow-500">
                                  {getOverallRating(selectedCar.stats)}/100
                                </span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </>
                  )}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="customize">
              {selectedCar ? (
                <CarCustomizer 
                  customization={selectedCar.customization}
                  onCustomizationChange={handleCustomizationChange}
                />
              ) : (
                <div className="text-center py-12">
                  <Settings className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl text-white mb-2">Select a Car</h3>
                  <p className="text-gray-400">Choose a car from your collection to customize.</p>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="shop" className="space-y-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-white mb-2">Car Dealership</h3>
                <p className="text-gray-400">Expand your collection with these iconic Australian cars</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {AVAILABLE_CARS.map((car) => {
                  const isOwned = user.garage.some(ownedCar => ownedCar.id === car.id);
                  
                  return (
                    <Card key={car.id} className="bg-gray-900/40 border-gray-700">
                      <CardContent className="p-4">
                        <div className="aspect-video bg-gray-800 rounded-lg mb-4 flex items-center justify-center">
                          <Car className="w-12 h-12 text-gray-600" />
                        </div>
                        
                        <div className="space-y-3">
                          <div>
                            <h4 className="font-medium text-white">{car.name}</h4>
                            <p className="text-sm text-gray-400">{car.model}</p>
                          </div>
                          
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-400">Overall Rating</span>
                            <div className="flex items-center gap-1">
                              <Star className="w-4 h-4 text-yellow-500" />
                              <span className="text-yellow-500">{getOverallRating(car.stats)}</span>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div className="flex justify-between">
                              <span className="text-gray-400">Power</span>
                              <span className="text-white">{car.stats.power}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-400">Handling</span>
                              <span className="text-white">{car.stats.handling}</span>
                            </div>
                          </div>
                          
                          <Button 
                            className={`w-full ${isOwned 
                              ? 'bg-gray-600 cursor-not-allowed' 
                              : 'bg-racing-red hover:bg-red-700'
                            }`}
                            onClick={() => !isOwned && handlePurchaseCar(car)}
                            disabled={isOwned}
                          >
                            {isOwned ? 'Owned' : 'Add to Garage'}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedGarage;
