
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

const Garage = () => {
  const cars = [
    {
      id: 'rx3-founders',
      name: 'Mazda RX-3 Savanna',
      year: '1973',
      edition: 'Founders Edition',
      image: '/lovable-uploads/20adddf2-9fe4-455f-8e10-8f4773d69892.png',
      special: true,
      stats: {
        speed: 85,
        acceleration: 90,
        handling: 75,
        braking: 65
      }
    },
    {
      id: 'rx3-yellow',
      name: 'Mazda RX-3 Savanna',
      year: '1975',
      edition: 'Competition Yellow',
      image: '/lovable-uploads/d926514d-0e22-447a-a4f4-c174e60d75a1.png',
      locked: true,
      stats: {
        speed: 82,
        acceleration: 88,
        handling: 73,
        braking: 62
      }
    },
    {
      id: 'rx3-silver',
      name: 'Mazda RX-3 Savanna',
      year: '1977',
      edition: 'Silver Street',
      image: '/lovable-uploads/a594cad8-350e-482b-b30c-73b539a00109.png',
      locked: true,
      stats: {
        speed: 80,
        acceleration: 85,
        handling: 78,
        braking: 68
      }
    }
  ];

  const renderStatBar = (value: number, color: string) => (
    <div className="w-full bg-gray-700 rounded-full h-2">
      <div 
        className={`h-2 rounded-full ${color}`}
        style={{ width: `${value}%` }}
      ></div>
    </div>
  );

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow py-16 bg-gradient-to-b from-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-racing-yellow mb-4">Your Garage</h1>
            <p className="text-gray-300">
              View and manage your collection of legendary Australian and Japanese cars.
            </p>
          </div>
          
          <Tabs defaultValue="collection" className="w-full">
            <TabsList className="w-full grid grid-cols-3 max-w-md mb-8">
              <TabsTrigger value="collection">Collection</TabsTrigger>
              <TabsTrigger value="upgrades">Upgrades</TabsTrigger>
              <TabsTrigger value="showcase">Showcase</TabsTrigger>
            </TabsList>
            
            <TabsContent value="collection">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {cars.map((car) => (
                  <Card 
                    key={car.id}
                    className={`bg-black/40 border-racing-yellow/20 overflow-hidden ${
                      car.special ? 'ring-2 ring-racing-yellow' : ''
                    } ${car.locked ? 'opacity-70' : ''}`}
                  >
                    <div className="relative h-48 w-full overflow-hidden">
                      <img 
                        src={car.image}
                        alt={car.name}
                        className={`w-full h-full object-cover ${car.locked ? 'filter blur-sm' : ''}`}
                      />
                      
                      {car.special && (
                        <div className="absolute top-4 right-4">
                          <Badge className="bg-gradient-to-r from-racing-yellow via-racing-red to-racing-yellow text-black font-bold">
                            Founders Edition
                          </Badge>
                        </div>
                      )}
                      
                      {car.locked && (
                        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                          <div className="p-4 rounded-full bg-black/80 border-2 border-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                              <path d="M7 11V7a5 5 0 0110 0v4"></path>
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <CardHeader>
                      <CardTitle className="text-racing-yellow flex justify-between items-center">
                        <span>{car.name}</span>
                        <span className="text-sm text-white">{car.year}</span>
                      </CardTitle>
                      <CardDescription className="text-gray-400">
                        {car.edition}
                      </CardDescription>
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-300">Speed</span>
                          <span className="text-racing-yellow">{car.stats.speed}</span>
                        </div>
                        {renderStatBar(car.stats.speed, 'bg-racing-red')}
                        
                        <div className="flex justify-between items-center">
                          <span className="text-gray-300">Acceleration</span>
                          <span className="text-racing-yellow">{car.stats.acceleration}</span>
                        </div>
                        {renderStatBar(car.stats.acceleration, 'bg-racing-yellow')}
                        
                        <div className="flex justify-between items-center">
                          <span className="text-gray-300">Handling</span>
                          <span className="text-racing-yellow">{car.stats.handling}</span>
                        </div>
                        {renderStatBar(car.stats.handling, 'bg-racing-green')}
                        
                        <div className="flex justify-between items-center">
                          <span className="text-gray-300">Braking</span>
                          <span className="text-racing-yellow">{car.stats.braking}</span>
                        </div>
                        {renderStatBar(car.stats.braking, 'bg-blue-500')}
                      </div>
                    </CardContent>
                    
                    <CardFooter>
                      <Button 
                        className={`w-full ${car.locked 
                          ? 'bg-gray-700 hover:bg-gray-600' 
                          : 'bg-racing-red hover:bg-red-700'} text-white`}
                        disabled={car.locked}
                      >
                        {car.locked ? 'Unlock Car' : 'Select Car'}
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
                
                {/* Add car slot */}
                <Card className="bg-black/20 border-dashed border-2 border-gray-600 flex flex-col items-center justify-center py-12 cursor-pointer hover:bg-black/30 transition-colors duration-300">
                  <div className="flex flex-col items-center space-y-4">
                    <div className="h-16 w-16 rounded-full bg-gray-800 flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                      </svg>
                    </div>
                    <span className="text-gray-400 font-medium">Add New Car</span>
                    <p className="text-gray-500 text-sm max-w-xs text-center">
                      Unlock more cars by participating in events or purchasing from the dealership
                    </p>
                  </div>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="upgrades">
              <div className="bg-black/40 border border-racing-yellow/20 rounded-lg p-8 text-center">
                <div className="flex flex-col items-center justify-center py-12 space-y-4">
                  <div className="h-20 w-20 rounded-full bg-gray-800 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                      <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-racing-yellow">Upgrade System Coming Soon</h3>
                  <p className="text-gray-400 max-w-lg">
                    Soon you'll be able to customize your cars with engine upgrades, turbochargers, 
                    body kits, liveries and more. Stay tuned for updates!
                  </p>
                  <Button className="mt-4 bg-racing-red hover:bg-red-700 text-white">
                    Get Notified
                  </Button>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="showcase">
              <div className="bg-black/40 border border-racing-yellow/20 rounded-lg p-8 text-center">
                <div className="flex flex-col items-center justify-center py-12 space-y-4">
                  <div className="h-20 w-20 rounded-full bg-gray-800 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                      <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                      <circle cx="12" cy="13" r="4"></circle>
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-racing-yellow">Showcase Coming Soon</h3>
                  <p className="text-gray-400 max-w-lg">
                    Soon you'll be able to create stunning showcases of your car collection to share with 
                    other players. Create custom backgrounds, lighting setups, and more!
                  </p>
                  <Button className="mt-4 bg-racing-red hover:bg-red-700 text-white">
                    Get Notified
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Garage;
