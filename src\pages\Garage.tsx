
import React from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import EnhancedGarage from '@/components/garage/EnhancedGarage';
import AchievementSystem from '@/components/achievements/AchievementSystem';
import { GarageBackground } from '@/components/graphics/EnhancedBackgrounds';
import Enhanced3DCarViewer from '@/components/graphics/Enhanced3DCarViewer';
import EnhancedCarCard from '@/components/graphics/EnhancedCarCard';
import { motion } from 'framer-motion';
import { placeholderImages } from '@/assets/images/cars';

const Garage = () => {
  return (
    <GarageBackground>
      <Navbar />

      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white">Your Garage</h1>
          <p className="text-gray-400 mt-2">Manage your car collection and achievements</p>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          <div className="xl:col-span-2">
            <EnhancedGarage />
          </div>

          <div>
            <AchievementSystem />
          </div>
        </div>
      </main>

      <Footer />
    </GarageBackground>
  );
};

export default Garage;
