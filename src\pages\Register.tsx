
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

const Register = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [remainingFounders, setRemainingFounders] = useState(63); // Random number for demo

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate form
    if (formData.password !== formData.confirmPassword) {
      toast.error("Passwords do not match");
      setIsSubmitting(false);
      return;
    }

    if (!formData.agreeToTerms) {
      toast.error("Please agree to the terms and conditions");
      setIsSubmitting(false);
      return;
    }

    // Simulate registration success
    setTimeout(() => {
      toast.success("Registration successful! Your Founders Edition Mazda RX-3 Savanna is ready in your garage!");
      navigate('/garage');
    }, 1500);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      
      <main className="flex-grow py-16 bg-gradient-to-b from-gray-900 to-black">
        <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="bg-black/40 border-racing-yellow/20">
            <CardHeader>
              <CardTitle className="text-2xl text-racing-yellow">Register Now</CardTitle>
              <CardDescription className="text-gray-300">
                Join Aussie Burnout Legends and claim your exclusive Founders Edition Mazda RX-3 Savanna Coupe!
              </CardDescription>
            </CardHeader>
            
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    name="username"
                    placeholder="Choose a username"
                    required
                    value={formData.username}
                    onChange={handleChange}
                    className="bg-gray-800 border-gray-700"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    className="bg-gray-800 border-gray-700"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    placeholder="••••••••"
                    required
                    minLength={8}
                    value={formData.password}
                    onChange={handleChange}
                    className="bg-gray-800 border-gray-700"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    placeholder="••••••••"
                    required
                    minLength={8}
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    className="bg-gray-800 border-gray-700"
                  />
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    id="agreeToTerms"
                    name="agreeToTerms"
                    type="checkbox"
                    checked={formData.agreeToTerms}
                    onChange={handleChange}
                    className="h-4 w-4 rounded border-gray-700 bg-gray-800 text-racing-red focus:ring-racing-red focus:ring-offset-gray-900"
                  />
                  <Label htmlFor="agreeToTerms" className="text-sm text-gray-400">
                    I agree to the <a href="#" className="text-racing-yellow hover:text-racing-red">Terms and Conditions</a>
                  </Label>
                </div>
                
                <div className="p-4 bg-racing-red/10 border border-racing-red/30 rounded-md">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-racing-yellow font-semibold">Founders Edition Cars</span>
                    <span className="bg-racing-red text-white text-xs px-2 py-1 rounded-full">
                      Limited
                    </span>
                  </div>
                  <p className="text-gray-300 text-sm mb-2">
                    Only <span className="text-racing-yellow font-bold">{remainingFounders}</span> Founders Edition 
                    Mazda RX-3 Savanna Coupes remaining!
                  </p>
                  <p className="text-gray-400 text-xs">
                    Register now to secure yours. After all 100 are claimed, 
                    this car will only be available as a rare trade or event prize.
                  </p>
                </div>
              </CardContent>
              
              <CardFooter>
                <Button 
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-racing-red hover:bg-red-700 text-white"
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Registering...
                    </>
                  ) : (
                    'Register & Claim Founders Edition'
                  )}
                </Button>
              </CardFooter>
            </form>
            
            <div className="px-6 pb-6 text-center">
              <p className="text-sm text-gray-400">
                Already registered? <a href="#" className="text-racing-yellow hover:text-racing-red">Sign in</a>
              </p>
            </div>
          </Card>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Register;
