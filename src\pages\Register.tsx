
import React from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import LoginForm from '@/components/auth/LoginForm';

const Register = () => {
  const navigate = useNavigate();

  const handleSuccess = () => {
    navigate('/garage');
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#000424] to-[#001440]">
      <Navbar />

      <main className="container mx-auto px-4 py-16">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white">Join Aussie Burnout Legends</h1>
          <p className="text-gray-400 mt-2">Create your account and start your journey</p>
        </div>

        <LoginForm onSuccess={handleSuccess} />
      </main>

      <Footer />
    </div>
  );
};

export default Register;
