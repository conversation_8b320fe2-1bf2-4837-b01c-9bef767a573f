
import React from 'react';
import { Car, Gauge, Trophy, Wrench, Users, Calendar, Flag, BarChart3, Flame } from 'lucide-react';
import GameFeatureCard from './GameFeatureCard';

const GameRoadmap: React.FC = () => {
  const features = [
    {
      title: 'Drag Racing',
      description: 'Test your car\'s acceleration in quarter-mile races against other players.',
      status: 'planned' as const,
      icon: <Flag />,
      tags: ['Multiplayer', 'Events', 'Rewards']
    },
    {
      title: 'Burnout Competitions',
      description: 'Show off your skills with tire-smoking burnouts and earn reputation points.',
      status: 'planned' as const,
      icon: <Flame />,
      tags: ['Competitions', 'Judging', 'Reputation']
    },
    {
      title: 'Car Customization',
      description: 'Modify every aspect of your car from engine to paint job.',
      status: 'planned' as const,
      icon: <Wrench />,
      tags: ['Parts', 'Visual', 'Performance']
    },
    {
      title: 'Dyno Testing',
      description: 'Measure your car\'s power and torque on our virtual dynamometer.',
      status: 'planned' as const,
      icon: <Gauge />,
      tags: ['Performance', 'Testing', 'Stats']
    },
    {
      title: 'Tournaments',
      description: 'Compete in organized tournaments with brackets and prizes.',
      status: 'planned' as const,
      icon: <Trophy />,
      tags: ['Competition', 'Prizes', 'Brackets']
    },
    {
      title: 'Car Collection',
      description: 'Collect classic Australian muscle cars and rare imports.',
      status: 'in-progress' as const,
      icon: <Car />,
      tags: ['Holden', 'Ford', 'Classics']
    },
    {
      title: 'Car Meets',
      description: 'Join virtual car meets to show off your rides and meet other players.',
      status: 'planned' as const,
      icon: <Users />,
      tags: ['Social', 'Community', 'Showcase']
    },
    {
      title: 'Event Calendar',
      description: 'Stay updated with scheduled races, tournaments and special events.',
      status: 'planned' as const,
      icon: <Calendar />,
      tags: ['Schedule', 'Planning', 'Notifications']
    },
    {
      title: 'Leaderboards',
      description: 'See how you rank against other players across different event types.',
      status: 'in-progress' as const,
      icon: <BarChart3 />,
      tags: ['Rankings', 'Stats', 'Competition']
    }
  ];

  return (
    <div className="container mx-auto py-10">
      <div className="text-center mb-10">
        <h2 className="text-3xl font-bold mb-2">Game Development Roadmap</h2>
        <p className="text-muted-foreground">
          Our plan for bringing you the ultimate Aussie car culture experience
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {features.map((feature, index) => (
          <GameFeatureCard
            key={index}
            title={feature.title}
            description={feature.description}
            status={feature.status}
            icon={feature.icon}
            tags={feature.tags}
          />
        ))}
      </div>
    </div>
  );
};

export default GameRoadmap;
